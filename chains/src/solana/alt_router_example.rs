use std::error::Error;
use std::str::FromStr;
use borsh::{BorshDeserialize, BorshSerialize};
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::instruction::{AccountMeta, Instruction};
use solana_sdk::compute_budget::ComputeBudgetInstruction;
use solana_sdk::message::{v0, Message, VersionedMessage};
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::{Keypair, Signer};
use solana_sdk::transaction::{Transaction, VersionedTransaction};
use solana_sdk::address_lookup_table::{AddressLookupTableAccount, state::AddressLookupTable};
use spl_associated_token_account::solana_program::pubkey;
use crate::solana::alt_manager::{<PERSON><PERSON>ana<PERSON>, AltError, DexType, IndexedAccountMeta};
use crate::solana::router::{SwapArgs, RouteConfig, Route, get_test_keypair, PROGRAM_ID, SWAP_DISCRIMINATOR, WSOL_MINT, USDC_MINT, WSOL_ACCOUNT, USDC_ACCOUNT, CONFIG_SEED, FlashLoanConfig};

// 使用 ALT 的紧凑交换参数
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
pub struct AltSwapArgs {
    pub route_config: RouteConfig,
    pub account_indices: Vec<IndexedAccountMeta>,
    pub amount_in: u64,
    pub expect_amount_out: u64,
    pub min_return: u64,
}

pub struct AltRouterManager {
    pub alt_manager: AltManager,
    pub rpc_client: RpcClient,
}

impl AltRouterManager {
    pub async fn new(rpc_url: &str, payer: Keypair) -> Result<Self, AltError> {
        let alt_manager = AltManager::recommended_setup(rpc_url, payer).await?;
        let rpc_client = RpcClient::new_with_commitment(
            rpc_url.to_string(),
            CommitmentConfig::processed()
        );

        Ok(Self {
            alt_manager,
            rpc_client,
        })
    }

    // 将传统的 SwapArgs 转换为使用 ALT 索引的版本
    pub async fn convert_to_alt_swap(
        &mut self,
        swap_args: &SwapArgs,
        all_addresses: Vec<Pubkey>,
    ) -> Result<AltSwapArgs, AltError> {
        // 获取所有地址的索引
        let indices = self.alt_manager.prepare_for_transaction(all_addresses).await?;

        // 构建索引化的账户元数据
        let mut account_indices = Vec::new();

        // 基础账户 (user, config, source_token, dest_token, source_mint, dest_mint)
        for i in 0..6 {
            account_indices.push(IndexedAccountMeta::new(
                indices[i],
                i == 0, // 只有 user 是 signer
                i == 0 || i == 2 || i == 3, // user, source_token, dest_token 是 writable
            ));
        }

        // DEX 特定账户
        for i in 6..indices.len() {
            account_indices.push(IndexedAccountMeta::new(
                indices[i],
                false,  // 大多数 DEX 账户不是 signer
                true,   // 大多数 DEX 账户需要 writable
            ));
        }

        Ok(AltSwapArgs {
            route_config: swap_args.route_config.clone(),
            account_indices,
            amount_in: swap_args.amount_in,
            expect_amount_out: swap_args.expect_amount_out,
            min_return: swap_args.min_return,
        })
    }

    // 构建使用 ALT 的版本化交易
    pub async fn build_alt_swap_transaction(
        &mut self,
        alt_swap_args: &AltSwapArgs,
        order_id: u64,
    ) -> Result<VersionedTransaction, Box<dyn Error>> {
        let program_id = Pubkey::from_str(PROGRAM_ID)?;

        // 序列化指令数据 - 使用与合约匹配的 SwapArgs 结构体
        let contract_swap_args = SwapArgs {
            route_config: alt_swap_args.route_config.clone(),
            amount_in: alt_swap_args.amount_in,
            expect_amount_out: alt_swap_args.expect_amount_out,
            min_return: alt_swap_args.min_return,
        };

        let mut instruction_data = Vec::new();
        instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
        contract_swap_args.serialize(&mut instruction_data)?;
        order_id.serialize(&mut instruction_data)?;

        // 将索引化账户元数据转换为常规账户元数据
        // 在 ALT 交易中，账户的 pubkey 通过 ALT 索引查找，但仍需要提供元数据
        let accounts: Vec<AccountMeta> = alt_swap_args.account_indices.iter().map(|indexed_meta| {
            // 使用 ALT 账户中对应索引的实际地址
            let actual_pubkey = if let Some(alt_address) = self.alt_manager.alt_account {
                // 从 ALT 中获取实际地址，如果失败则使用默认值
                self.alt_manager.local_registry.get_address(indexed_meta.index)
                    .unwrap_or(Pubkey::default())
            } else {
                Pubkey::default()
            };

            AccountMeta {
                pubkey: actual_pubkey,
                is_signer: indexed_meta.is_signer,
                is_writable: indexed_meta.is_writable,
            }
        }).collect();

        // 创建 CU 预算指令 - 设置足够的计算单元
        let cu_limit_instruction = ComputeBudgetInstruction::set_compute_unit_limit(1_000_000); // 1M CU
        let cu_price_instruction = ComputeBudgetInstruction::set_compute_unit_price(1000); // 微lamports

        // 创建主指令
        let swap_instruction = Instruction {
            program_id,
            accounts,
            data: instruction_data,
        };

        // 获取 ALT 账户
        let alt_address = self.alt_manager.alt_account
            .ok_or("ALT 账户未初始化")?;

        let alt_account_data = self.rpc_client.get_account(&alt_address)?;
        let alt_account = AddressLookupTableAccount {
            key: alt_address,
            addresses: AddressLookupTable::deserialize(&alt_account_data.data)?.addresses.to_vec(),
        };

        // 构建版本化消息 - 包含 CU 预算指令
        let payer = &self.alt_manager.payer.pubkey();
        let recent_blockhash = self.rpc_client.get_latest_blockhash()?;

        let message = v0::Message::try_compile(
            payer,
            &[cu_limit_instruction, cu_price_instruction, swap_instruction],
            &[alt_account],
            recent_blockhash,
        )?;

        let versioned_message = VersionedMessage::V0(message);

        // 签名交易
        let versioned_transaction = VersionedTransaction::try_new(
            versioned_message,
            &[&self.alt_manager.payer]
        )?;

        Ok(versioned_transaction)
    }

    // 构建使用 ALT 的闪电贷交换交易
    pub async fn build_alt_flash_loan_swap_transaction(
        &mut self,
        dex_type: DexType,
        amount_in: u64,
        flash_loan_amount: u64,
        order_id: u64,
    ) -> Result<VersionedTransaction, Box<dyn Error>> {
        let program_id = Pubkey::from_str(PROGRAM_ID)?;
        let kamino_program = Pubkey::from_str("KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD")?;

        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
        let usdc_mint = Pubkey::from_str(USDC_MINT)?;
        let wsol_account = Pubkey::from_str(WSOL_ACCOUNT)?;
        let usdc_account = Pubkey::from_str(USDC_ACCOUNT)?;
        let payer = self.alt_manager.payer.pubkey();

        // 收集所有需要的地址
        let mut all_addresses = vec![
            payer,                                    // user/user_transfer_authority
            Pubkey::find_program_address(&[CONFIG_SEED], &Pubkey::from_str(PROGRAM_ID)?).0, // config
            wsol_account,                            // source_token/user_destination_liquidity
            usdc_account,                            // dest_token/user_source_liquidity
            wsol_mint,                              // source_mint/reserve_liquidity_mint
            usdc_mint,                              // dest_mint
        ];

        // 添加闪电贷相关地址
        all_addresses.extend(self.alt_manager.get_kamino_addresses());

        // 添加 DEX 特定地址
        all_addresses.extend(match dex_type {
            DexType::Raydium => self.alt_manager.get_raydium_addresses(),
            DexType::Meteora => self.alt_manager.get_meteora_addresses(),
            DexType::PumpFun => self.alt_manager.get_pump_addresses(),
            DexType::Orca => self.alt_manager.get_orca_addresses(),
        });

        // 获取所有地址的索引
        let indices = self.alt_manager.prepare_for_transaction(all_addresses.clone()).await?;

        // 构建闪电贷借款指令数据
        let flash_borrow_discriminator = [135, 231, 52, 167, 7, 52, 212, 193];
        let mut flash_borrow_data = Vec::new();
        flash_borrow_data.extend_from_slice(&flash_borrow_discriminator);
        flash_loan_amount.serialize(&mut flash_borrow_data)?;

        println!("闪电贷 params {:?}", flash_borrow_data);

        // 闪电贷借款指令账户
        let flash_borrow_accounts = vec![
            AccountMeta::new(payer, true),                    // user_transfer_authority
            AccountMeta::new_readonly(all_addresses[8], false), // lending_market_authority
            AccountMeta::new_readonly(all_addresses[9], false), // lending_market
            AccountMeta::new(all_addresses[10], false),         // reserve
            AccountMeta::new_readonly(all_addresses[11], false),       // reserve_liquidity_mint
            AccountMeta::new(all_addresses[12], false),         // reserve_source_liquidity
            AccountMeta::new(all_addresses[13], false),             // user_destination_liquidity
            AccountMeta::new(all_addresses[14], false),        // reserve_liquidity_fee_receiver
            AccountMeta::new(all_addresses[15], false), // sysvar_info
            AccountMeta::new(all_addresses[16], false), // token_program
            AccountMeta::new(all_addresses[17], false), // token_program
            AccountMeta::new(all_addresses[18], false), // token_program
        ];

        println!("闪电贷借款账户:");
        for (i, account) in flash_borrow_accounts.iter().enumerate() {
            println!("  [{}] {} (signer: {}, writable: {})",
                     i, account.pubkey, account.is_signer, account.is_writable);
        }

        let flash_borrow_instruction = Instruction {
            program_id: kamino_program,
            accounts: flash_borrow_accounts,
            data: flash_borrow_data,
        };

        // 构建 DEX 交换指令
        let route = Route {
            dex_id: match dex_type {
                DexType::Raydium => 0,
                DexType::Meteora => 2,
                DexType::PumpFun => 5,
                DexType::Orca => 4,
            },
            input_mint: wsol_mint,
            output_mint: usdc_mint,
        };

        let route_config = RouteConfig {
            routing_mode_id: 0,
            routes: vec![route],
            amount_in,
            min_amount_out: 1,
            max_slippage_bps: 300,
            flash_loan: None,
        };

        let swap_args = SwapArgs {
            route_config: route_config.clone(),
            amount_in,
            expect_amount_out: 1,
            min_return: 1,
        };

        let mut swap_instruction_data = Vec::new();
        swap_instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
        swap_args.serialize(&mut swap_instruction_data)?;
        order_id.serialize(&mut swap_instruction_data)?;

        // DEX 交换指令账户（基于 DEX 类型）
        let swap_accounts: Vec<AccountMeta> = (0..6).map(|i| {
            let actual_pubkey = self.alt_manager.local_registry.get_address(indices[i])
                .unwrap_or(Pubkey::default());
            AccountMeta {
                pubkey: actual_pubkey,
                is_signer: i == 0,
                is_writable: i == 0 || i == 2 || i == 3,
            }
        }).chain(
            // DEX 特定账户
            (19..(19 + match dex_type {
                DexType::Raydium => 17,
                DexType::Meteora => 16,
                DexType::PumpFun => 17,
                DexType::Orca => 13,
            })).map(|i| {
                let actual_pubkey = self.alt_manager.local_registry.get_address(indices[i])
                    .unwrap_or(Pubkey::default());
                AccountMeta {
                    pubkey: actual_pubkey,
                    is_signer: false,
                    is_writable: true,
                }
            })
        ).collect();

        let swap_instruction = Instruction {
            program_id,
            accounts: swap_accounts,
            data: swap_instruction_data,
        };

        // 构建闪电贷还款指令数据
        let flash_repay_discriminator = [185, 117, 0, 203, 96, 245, 180, 186];
        let mut flash_repay_data = Vec::new();
        flash_repay_data.extend_from_slice(&flash_repay_discriminator);
        flash_loan_amount.serialize(&mut flash_repay_data)?;
        2u8.serialize(&mut flash_repay_data)?; // borrow_instruction_index

        // 闪电贷还款指令账户
        let flash_repay_accounts = vec![
            AccountMeta::new(payer, true),                    // user_transfer_authority
            AccountMeta::new_readonly(all_addresses[8], false), // lending_market_authority
            AccountMeta::new_readonly(all_addresses[9], false), // lending_market
            AccountMeta::new(all_addresses[10], false),         // reserve
            AccountMeta::new_readonly(all_addresses[11], false),       // reserve_liquidity_mint
            AccountMeta::new(all_addresses[12], false),         // reserve_source_liquidity
            AccountMeta::new(all_addresses[13], false),             // user_destination_liquidity
            AccountMeta::new(all_addresses[14], false),        // reserve_liquidity_fee_receiver
            AccountMeta::new(all_addresses[15], false), // sysvar_info
            AccountMeta::new(all_addresses[16], false), // token_program
            AccountMeta::new(all_addresses[17], false), // token_program
            AccountMeta::new(all_addresses[18], false), // token_program
        ];

        println!("闪电贷还款账户:");
        for (i, account) in flash_repay_accounts.iter().enumerate() {
            println!("  [{}] {} (signer: {}, writable: {})",
                     i, account.pubkey, account.is_signer, account.is_writable);
        }

        let flash_repay_instruction = Instruction {
            program_id: kamino_program,
            accounts: flash_repay_accounts,
            data: flash_repay_data,
        };

        // 创建 CU 预算指令
        let cu_limit_instruction = ComputeBudgetInstruction::set_compute_unit_limit(1_400_000); // 1.4M CU for flash loan
        let cu_price_instruction = ComputeBudgetInstruction::set_compute_unit_price(1000);

        // 获取 ALT 账户
        let alt_address = self.alt_manager.alt_account
            .ok_or("ALT 账户未初始化")?;

        let alt_account_data = self.rpc_client.get_account(&alt_address)?;
        let alt_account = AddressLookupTableAccount {
            key: alt_address,
            addresses: AddressLookupTable::deserialize(&alt_account_data.data)?.addresses.to_vec(),
        };

        // 构建版本化消息 - 包含三个指令：闪电贷借款、DEX交换、闪电贷还款
        let payer = &self.alt_manager.payer.pubkey();
        let recent_blockhash = self.rpc_client.get_latest_blockhash()?;

        let message = v0::Message::try_compile(
            payer,
            &[cu_limit_instruction, cu_price_instruction, flash_borrow_instruction, swap_instruction, flash_repay_instruction],
            &[alt_account],
            recent_blockhash,
        )?;

        let versioned_message = VersionedMessage::V0(message);

        // 签名交易
        let versioned_transaction = VersionedTransaction::try_new(
            versioned_message,
            &[&self.alt_manager.payer]
        )?;

        Ok(versioned_transaction)
    }

    // 执行 ALT 优化的闪电贷单 DEX 交换
    pub async fn execute_alt_flash_loan_single_dex_swap(
        &mut self,
        dex_type: DexType,
        amount_in: u64,
        flash_loan_amount: u64,
    ) -> Result<String, Box<dyn Error>> {
        println!("执行使用 ALT 和闪电贷的单 DEX 交换...");

        // 添加 Kamino 和 DEX 特定地址
        self.alt_manager.add_kamino_address().await?;
        self.alt_manager.add_dex_addresses(dex_type).await?;

        // 构建版本化交易
        let versioned_transaction = self.build_alt_flash_loan_swap_transaction(
            dex_type, amount_in, flash_loan_amount, 88888
        ).await?;

        // 显示数据压缩效果
        let original_accounts = 6 + 13 + match dex_type { // 基础 + Kamino + DEX
            DexType::Raydium => 18,
            DexType::Meteora => 16,
            DexType::PumpFun => 17,
            DexType::Orca => 13,
        };

        println!("闪电贷交换数据压缩效果:");
        println!("  原始账户数: {}", original_accounts);
        println!("  闪电贷金额: {} SOL", flash_loan_amount as f64 / 1e9);
        println!("  交换金额: {} SOL", amount_in as f64 / 1e9);
        println!("  指令数量: 5 (CU预算x2 + 闪电贷借款 + DEX交换 + 闪电贷还款)");

        // 发送交易
        let signature = self.rpc_client.send_and_confirm_transaction(&versioned_transaction)?;

        println!("ALT 闪电贷交换交易成功: {}", signature);
        Ok(signature.to_string())
    }

    // 执行 ALT 优化的单 DEX 交换
    pub async fn execute_alt_single_dex_swap(
        &mut self,
        dex_type: DexType,
        amount_in: u64,
    ) -> Result<String, Box<dyn Error>> {
        println!("执行使用 ALT 的单 DEX 交换...");

        // 添加 DEX 特定地址
        self.alt_manager.add_dex_addresses(dex_type).await?;

        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
        let usdc_mint = Pubkey::from_str(USDC_MINT)?;
        let wsol_account = Pubkey::from_str(WSOL_ACCOUNT)?;
        let usdc_account = Pubkey::from_str(USDC_ACCOUNT)?;
        let payer = self.alt_manager.payer.pubkey();

        // 构建路由配置
        let route = Route {
            dex_id: match dex_type {
                DexType::Raydium => 0,
                DexType::Meteora => 2,
                DexType::PumpFun => 5,
                DexType::Orca => 4,
            },
            input_mint: wsol_mint,
            output_mint: usdc_mint,
        };

        let route_config = RouteConfig {
            routing_mode_id: 0,
            routes: vec![route],
            amount_in,
            min_amount_out: 1,
            max_slippage_bps: 300,
            flash_loan: None,
        };

        let swap_args = SwapArgs {
            route_config: route_config.clone(),
            amount_in,
            expect_amount_out: 1,
            min_return: 1,
        };

        // 收集所有需要的地址
        let mut all_addresses = vec![
            payer,                                    // user
            Pubkey::find_program_address(&[CONFIG_SEED], &Pubkey::from_str(PROGRAM_ID)?).0, // config
            wsol_account,                            // source_token
            usdc_account,                            // dest_token
            wsol_mint,                              // source_mint
            usdc_mint,                              // dest_mint
        ];

        // 添加 DEX 特定地址
        all_addresses.extend(match dex_type {
            DexType::Raydium => self.alt_manager.get_raydium_addresses(),
            DexType::Meteora => self.alt_manager.get_meteora_addresses(),
            DexType::PumpFun => self.alt_manager.get_pump_addresses(),
            DexType::Orca => self.alt_manager.get_orca_addresses(),
        });

        // 转换为 ALT 版本
        let alt_swap_args = self.convert_to_alt_swap(&swap_args, all_addresses).await?;

        println!("ALT 交换参数: {:?}", alt_swap_args);

        // 构建版本化交易
        let versioned_transaction = self.build_alt_swap_transaction(&alt_swap_args, 12345).await?;

        // 显示数据压缩效果
        let original_accounts = 6 + match dex_type {
            DexType::Raydium => 18,
            DexType::Meteora => 16,
            DexType::PumpFun => 17,
            DexType::Orca => 13,
        };
        let compressed_indices = alt_swap_args.account_indices.len();

        println!("数据压缩效果:");
        println!("  原始账户数: {}", original_accounts);
        println!("  压缩索引数: {}", compressed_indices);
        println!("  原始大小: {} 字节", original_accounts * 32);
        println!("  压缩大小: {} 字节", compressed_indices * 1);
        println!("  压缩率: {:.1}%",
                (1.0 - compressed_indices as f64 * 1.0 / (original_accounts as f64 * 32.0)) * 100.0);

        // 发送交易
        let signature = self.rpc_client.send_and_confirm_transaction(&versioned_transaction)?;

        println!("ALT 交换交易成功: {}", signature);
        Ok(signature.to_string())
    }

    // 执行 ALT 优化的多 DEX 线性路由
    pub async fn execute_alt_multi_dex_swap(
        &mut self,
        amount_in: u64,
    ) -> Result<String, Box<dyn Error>> {
        println!("执行使用 ALT 的多 DEX 线性路由...");

        // 添加所有 DEX 地址
        self.alt_manager.add_dex_addresses(DexType::Raydium).await?;
        self.alt_manager.add_dex_addresses(DexType::Meteora).await?;
        self.alt_manager.add_kamino_address().await?;

        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
        let usdc_mint = Pubkey::from_str(USDC_MINT)?;
        let wsol_account = Pubkey::from_str(WSOL_ACCOUNT)?;
        let usdc_account = Pubkey::from_str(USDC_ACCOUNT)?;
        let payer = self.alt_manager.payer.pubkey();

        // 构建多步路由 (Raydium -> Meteora)
        let route1 = Route {
            dex_id: 0, // Raydium
            input_mint: wsol_mint,
            output_mint: usdc_mint,
        };

        let route2 = Route {
            dex_id: 2, // Meteora
            input_mint: usdc_mint,
            output_mint: wsol_mint,
        };

        let route_config = RouteConfig {
            routing_mode_id: 0,
            routes: vec![route1, route2],
            amount_in,
            min_amount_out: 1,
            max_slippage_bps: 300,
            flash_loan: Some(FlashLoanConfig {
                provider: 0,
                provider_program: pubkey!("KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD"),
                borrower: pubkey!("8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG"),
                amount: 100_000_000, // 0.1 SOL
                max_fee_bps: 0,
            }),
        };

        let swap_args = SwapArgs {
            route_config: route_config.clone(),
            amount_in,
            expect_amount_out: 1,
            min_return: 1,
        };

        // 收集所有地址 (基础 + Raydium + Meteora)
        let mut all_addresses = vec![
            payer,
            Pubkey::find_program_address(&[CONFIG_SEED], &Pubkey::from_str(PROGRAM_ID)?).0,
            wsol_account,
            wsol_account, // 最终输出也是 WSOL
            wsol_mint,
            wsol_mint,    // 最终输出 mint
        ];
        all_addresses.push(pubkey!("KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD"));
        all_addresses.push(pubkey!("2Aw1uS9o3CV2qurdysR9vLR9qtoWzL5xwX7oJcnrY7TA"));
        all_addresses.push(pubkey!("Dx8iy2o46sK1DzWbEcznqSKeLbLVeu7otkibA3WohGAj"));
        all_addresses.push(pubkey!("H6rHXmXoCQvq8Ue81MqNh7ow5ysPa1dSozwW3PU1dDH6"));
        all_addresses.push(pubkey!("6gTJfuPHEg6uRAijRkMqNc9kan4sVZejKMxmvx2grT1p"));
        all_addresses.push(pubkey!("So11111111111111111111111111111111111111112"));
        all_addresses.push(pubkey!("ywaaLvG7t1vXJo8sT3UzE8yzzZtxLM7Fmev64Jbooye"));
        all_addresses.push(pubkey!("8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG"));
        all_addresses.push(pubkey!("EQ7hw63aBS7aPQqXsoxaaBxiwbEzaAiY9Js6tCekkqxf"));
        all_addresses.push(pubkey!("KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD"));
        all_addresses.push(pubkey!("KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD"));
        all_addresses.push(pubkey!("Sysvar1nstructions1111111111111111111111111"));
        all_addresses.push(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"));
        all_addresses.extend(self.alt_manager.get_raydium_addresses());
        all_addresses.extend(self.alt_manager.get_meteora_addresses());

        // 转换为 ALT 版本
        let alt_swap_args = self.convert_to_alt_swap(&swap_args, all_addresses.clone()).await?;

        // 显示多 DEX 压缩效果
        println!("多 DEX 路由数据压缩效果:");
        println!("  总地址数: {}", all_addresses.len());
        println!("  原始大小: {} 字节", all_addresses.len() * 32);
        println!("  压缩大小: {} 字节", alt_swap_args.account_indices.len() * 1);
        println!("  压缩率: {:.1}%",
                (1.0 - alt_swap_args.account_indices.len() as f64 / (all_addresses.len() as f64 * 32.0)) * 100.0);

        // 构建并发送交易
        let versioned_transaction = self.build_alt_swap_transaction(&alt_swap_args, 99999).await?;
        let signature = self.rpc_client.send_and_confirm_transaction(&versioned_transaction)?;

        println!("ALT 多 DEX 路由交易成功: {}", signature);
        Ok(signature.to_string())
    }

    // 显示 ALT 路由器状态
    pub async fn display_router_status(&self) {
        println!("=== ALT 路由器状态 ===");
        self.alt_manager.display_status().await;

        if let Ok((count, is_synced)) = self.alt_manager.get_alt_info().await {
            println!("支持的最大并发地址: {}", count);
            println!("理论最大压缩率: {:.1}%", (1.0 - 1.0 / 32.0) * 100.0);
        }
    }
}

// 使用示例
pub async fn example_alt_router_usage() -> Result<(), Box<dyn Error>> {
    println!("=== ALT 路由器使用示例 ===\n");

    let rpc_url = "https://www.techxk.com";
    let payer = get_test_keypair()?;

    // 初始化 ALT 路由器管理器
    let mut alt_router = AltRouterManager::new(rpc_url, payer).await?;

    // 显示初始状态
    alt_router.display_router_status().await;

    // 执行单 DEX 交换测试
    // println!("\n1. 执行 Raydium 单 DEX ALT 交换...");
    // let _signature1 = alt_router.execute_alt_single_dex_swap(
    //     DexType::Raydium,
    //     10_000_000 // 0.01 SOL
    // ).await?;

    // 执行闪电贷单 DEX 交换测试
    println!("\n2. 执行 Raydium 闪电贷 ALT 交换...");
    let _signature2 = alt_router.execute_alt_flash_loan_single_dex_swap(
        DexType::Raydium,
        50_000_000,  // 0.05 SOL 交换金额
        1000_000_000_000  // 0.1 SOL 闪电贷金额
    ).await?;

    // 执行多 DEX 路由测试
    // println!("\n3. 执行多 DEX ALT 路由交换...");
    // let _signature3 = alt_router.execute_alt_multi_dex_swap(
    //     10_000_000 // 0.01 SOL
    // ).await?;

    // 显示最终状态
    println!("\n最终状态:");
    alt_router.display_router_status().await;

    println!("\nALT 路由器示例完成!");
    Ok(())
}
