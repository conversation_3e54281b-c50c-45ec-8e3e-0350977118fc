[package]
name = "markets"
version = "0.1.0"
edition = "2024"

[dependencies]
serde = { workspace = true, features = ["derive"] }
serde_json.workspace = true
tungstenite.workspace = true
tokio-tungstenite.workspace = true
url.workspace = true
bytes.workspace = true
tracing.workspace = true
tokio.workspace = true
thiserror.workspace = true
reqwest.workspace = true
hmac.workspace = true
sha2.workspace = true
hex.workspace = true
chrono.workspace = true
rand.workspace = true
futures.workspace = true

common = { path = "../common" }




[dev-dependencies]
tokio = { version = "1.44.2", features = ["rt", "rt-multi-thread", "macros", "sync"] }
