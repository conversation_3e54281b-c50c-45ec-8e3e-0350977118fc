use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::Arc;
use std::time::Instant;
use rust_decimal::Decimal;
use rust_decimal::prelude::*;
use solana_sdk::pubkey::Pubkey;
use tracing::{debug, error, info, warn};

// 标识 DEX 来源
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash)]
pub enum DexId {
    RaydiumClmm,
    OrcaWhirlpools,
    MeteoraDlmm,
    PumpSwap
}

// 标准化池子信息
#[derive(Debug, Clone)]
pub struct StandardizedPoolInfo {
    pub dex: DexId,
    pub pool_address: Pubkey,
    pub token_mint_0: Pubkey,
    pub token_mint_1: Pubkey,
    pub decimals_0: u8,
    pub decimals_1: u8,
    // 价格表示：1 单位 Token0 值多少 Token1
    // 使用 Decimal 保证精度
    pub price_t1_per_t0: Decimal,
    pub tick_spacing: u16,
    pub tick_current: i32,
    // TODO: Add liquidity info later for simulation
    // TODO: Add fee info later for simulation
}

impl StandardizedPoolInfo {
    /// 获取从 Token0 兑换到 Token1 的汇率 (不考虑费用/滑点)
    /// 返回: 多少 Token1 可以换 1 个 Token0
    pub fn rate_t0_to_t1(&self) -> Option<Decimal> {
        if self.price_t1_per_t0.is_sign_negative() || self.price_t1_per_t0.is_zero() {
            // 无效或零价格无法提供有效汇率
            None
        } else {
            Some(self.price_t1_per_t0)
        }
    }

    /// 获取从 Token1 兑换到 Token0 的汇率 (不考虑费用/滑点)
    /// 返回: 多少 Token0 可以换 1 个 Token1
    pub fn rate_t1_to_t0(&self) -> Option<Decimal> {
        if self.price_t1_per_t0.is_sign_negative() || self.price_t1_per_t0.is_zero() {
            // 价格为0时，反向汇率是无穷大或未定义
            None
        } else {
            Decimal::ONE.checked_div(self.price_t1_per_t0)
        }
    }
}



// TradeDirection enum needs to be defined somewhere accessible
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TradeDirection {
    T0toT1,
    T1toT0,
}


// 图的边结构
#[derive(Debug, Clone)]
struct Edge {
    // 起始节点（Token）的索引
    to_node_idx: usize,
    // 终止节点（Token）的索引
    from_node_idx: usize,
    // 边的权重 (-log(rate))
    pool_info: Arc<StandardizedPoolInfo>,
    // 关联的池子信息
    direction: TradeDirection,
    // 交易方向
    weight: f64,
}

// 表示套利路径中的一步
#[derive(Debug, Clone)]
pub struct ArbitrageStep {
    // 起始代币
    pub to_token: Pubkey,
    // 目标代币
    pub rate: Decimal,
    // 这一步的理论汇率
    pub pool_info: Arc<StandardizedPoolInfo>,
    // 使用的池子
    pub direction: TradeDirection,
    // 交易方向
    pub from_token: Pubkey,
}

// 表示一个潜在的套利路径（环路）
#[derive(Debug, Clone)]
pub struct ArbitragePath {
    // 路径步骤
    pub steps: Vec<ArbitrageStep>,
    // 理论利润因子 (rate1 * rate2 * ...) > 1.0
    pub profit_factor: f64,
}


// Validate cycle length [3, MAX_ARBITRAGE_STEPS]
const MAX_ARBITRAGE_STEPS: usize = 4;
// Production threshold might be higher due to fees/slippage later
// Example: 0.05% theoretical profit
const MIN_PROFIT_FACTOR: f64 = 1.03;

/// 主函数：查找套利机会
/// 输入: 当前所有标准化池子数据的快照 (Arc<StandardizedPoolInfo> 方便共享)
/// 输出: 发现的潜在套利路径列表
pub fn find_arbitrage_opportunities(
    pools: &Vec<Arc<StandardizedPoolInfo>>,
    start_tokens: &HashSet<Pubkey>,
) -> Result<Vec<ArbitragePath>, String> {
    if pools.is_empty() || start_tokens.is_empty() {
        debug!("No pools or no specified start tokens provided.");
        return Ok(Vec::new());
    }

    let now = Instant::now();

    // 1. Build Graph
    let (token_to_idx, idx_to_token, edges) = build_graph(pools)?;
    let num_tokens = idx_to_token.len();
    if num_tokens < 2 || edges.is_empty() {
        info!("Not enough tokens ({}) or edges ({}) for arbitrage.", num_tokens, edges.len());
        return Ok(Vec::new());
    }
    info!("Graph built: {} tokens, {} edges", num_tokens, edges.len());

    // --- 查找起始代币在图中的索引 ---
    let start_token_indices: HashSet<usize> = start_tokens
        .iter()
        .filter_map(|token_pk| token_to_idx.get(token_pk).copied())
        .collect();

    if start_token_indices.is_empty() {
        warn!("None of the specified start tokens were found in the graph's pools.");
        return Ok(Vec::new());
    }
    info!("Searching for cycles starting/ending at indices: {:?}", start_token_indices);

    // 2. Bellman-Ford Initialization (Revised)
    // Initialize all distances to 0, allowing detection of any negative cycle.
    let mut dist: Vec<f64> = vec![0.0; num_tokens];
    // Store predecessor index AND the specific edge used for reconstruction
    let mut predecessor: Vec<Option<(usize, Arc<Edge>)>> = vec![None; num_tokens];

    // 3. Relax Edges V times (V = num_tokens)
    for i in 1..=num_tokens { // Iterate V times
        let mut updated_in_iteration = false;
        // Use indexing for potentially faster iteration if edges Vec is large
        for edge_idx in 0..edges.len() {
            let edge = &edges[edge_idx]; // Borrow edge

            // Check if 'from' node is reachable (dist != infinity is implied by 0 init)
            let new_dist = dist[edge.from_node_idx] + edge.weight;

            // Check for potential relaxation using epsilon comparison
            if new_dist < dist[edge.to_node_idx] - FLOATING_POINT_EPSILON {
                // Check for large jumps indicating instability (optional sanity check)
                // if (new_dist - dist[edge.to_node_idx]).abs() > some_large_threshold {
                //     warn!("Potential instability detected during relaxation for edge {:?}", edge);
                // }

                dist[edge.to_node_idx] = new_dist;
                // Clone the Arc<Edge> for the predecessor map
                predecessor[edge.to_node_idx] = Some((edge.from_node_idx, Arc::new(edge.clone())));
                updated_in_iteration = true;

                // If this is the V-th iteration, mark this node for cycle check
                if i == num_tokens {
                    // Node edge.to_node_idx is part of or reachable from a neg cycle
                    // We will process these nodes later.
                }
            }
        }
        // info!("Bellman-Ford iteration {} complete. Updated {}", i, updated_in_iteration);
        // Note: No early exit break needed when checking for *all* cycles
    }

    // 4. Identify Nodes Involved in Negative Cycles
    let mut nodes_in_neg_cycle = HashSet::new();
    // Check again using the V-th iteration results (or check which nodes were updated in V-th pass)
    for edge_idx in 0..edges.len() {
        let edge = &edges[edge_idx];
        let new_dist = dist[edge.from_node_idx] + edge.weight;
        if new_dist < dist[edge.to_node_idx] - FLOATING_POINT_EPSILON {
            // Node edge.to_node_idx is confirmed to be affected by a negative cycle
            // Add the 'from' node too, as it leads into the cycle / is part of it
            nodes_in_neg_cycle.insert(edge.to_node_idx);
            nodes_in_neg_cycle.insert(edge.from_node_idx);
            // info!("Node {} detected in V-th pass via edge from {}", edge.to_node_idx, edge.from_node_idx);
        }
    }

    if nodes_in_neg_cycle.is_empty() {
        info!("No negative cycles detected after Bellman-Ford.");
        return Ok(Vec::new());
    }
    debug!("Nodes potentially in negative cycles: {:?}", nodes_in_neg_cycle);


    // 5. Reconstruct Cycles and Prevent Duplicates
    let mut arbitrage_paths = Vec::new();
    // Keep track of nodes already part of a reported cycle in *this run*
    let mut nodes_in_reported_cycles = HashSet::new();

    // --- 只从受负环影响的 *起始代币* 开始回溯 ---
    // Iterate only over the intersection of nodes affected by cycles AND desired start nodes
    let potential_start_nodes: HashSet<usize> = nodes_in_neg_cycle
        .intersection(&start_token_indices)
        .copied() // Copy the indices from the intersection
        .collect();

    if potential_start_nodes.is_empty() {
        info!("Nodes affected by negative cycles do not include any of the specified start tokens.");
        return Ok(Vec::new());
    }
    debug!("Starting cycle reconstruction from potential start nodes: {:?}", potential_start_nodes);

    for &start_node_idx in &potential_start_nodes {
        // If this node was already part of a reported cycle, skip traceback from here
        if nodes_in_reported_cycles.contains(&start_node_idx) {
            info!("Node {} already in reported cycle, skipping traceback.", start_node_idx);
            continue;
        }

        match reconstruct_cycle_robust(start_node_idx, &predecessor, num_tokens) {
            Ok(cycle_edges) => {
                // Validate cycle
                if cycle_edges.is_empty() {
                    info!("Reconstruction yielded empty cycle for start node {}", start_node_idx);
                    continue;
                }
                // Minimal check: cycle has at least 3 steps for triangular
                if cycle_edges.len() < 2 || cycle_edges.len() > MAX_ARBITRAGE_STEPS {
                    info!("Cycle from {} has length {}, outside range [2, {}].", start_node_idx, cycle_edges.len(), MAX_ARBITRAGE_STEPS);
                    continue;
                }

                // --- 关键过滤：检查环路的起点/终点是否是我们允许的起始代币 ---
                let cycle_start_token_idx = cycle_edges.first().map(|e| e.from_node_idx);
                let cycle_end_token_idx = cycle_edges.last().map(|e| e.to_node_idx);

                // Check if the cycle actually starts and ends at the node we started tracing from,
                // and if that node is one of our allowed start_token_indices.
                // (reconstruct_cycle_robust should ensure start==end index consistency)
                if cycle_start_token_idx != Some(start_node_idx) || cycle_end_token_idx != Some(start_node_idx) {
                    info!("Reconstructed cycle from {} doesn't seem to start/end correctly at {}. Cycle start={}, end={}. Skipping.",
                           start_node_idx, start_node_idx,
                           cycle_start_token_idx.map_or("None".to_string(), |i| i.to_string()),
                           cycle_end_token_idx.map_or("None".to_string(), |i| i.to_string()));
                    continue; // Cycle doesn't loop back to the expected start token index
                }
                // No need to check start_token_indices.contains(&start_node_idx) again,

                // Check for duplicates based on nodes involved
                let mut cycle_nodes = HashSet::new();
                let mut all_nodes_already_reported = true;
                for edge in &cycle_edges {
                    cycle_nodes.insert(edge.from_node_idx);
                    cycle_nodes.insert(edge.to_node_idx);
                    if !nodes_in_reported_cycles.contains(&edge.from_node_idx) {
                        all_nodes_already_reported = false;
                    }
                    if !nodes_in_reported_cycles.contains(&edge.to_node_idx) {
                        all_nodes_already_reported = false;
                    }
                }

                if all_nodes_already_reported {
                    info!("Cycle found from {} contains only nodes already reported in other cycles, skipping.", start_node_idx);
                    continue; // Avoid duplicates
                }


                // Convert edges to ArbitrageSteps and calculate profit factor
                let mut steps = Vec::new();
                let mut total_log_weight = 0.0;
                let mut path_valid = true;

                for edge_arc in &cycle_edges {
                    total_log_weight += edge_arc.weight;
                    let from_token = idx_to_token[edge_arc.from_node_idx];
                    let to_token = idx_to_token[edge_arc.to_node_idx];
                    let rate = match edge_arc.direction {
                        TradeDirection::T0toT1 => edge_arc.pool_info.rate_t0_to_t1(),
                        TradeDirection::T1toT0 => edge_arc.pool_info.rate_t1_to_t0(),
                    };

                    if let Some(r) = rate {
                        // Check rate validity again before adding step
                        // if r.is_sign_negative() || r.is_zero() || r.is_nan() {
                        if r.is_sign_negative() || r.is_zero() {
                            info!("Invalid rate ({}) calculated for step in cycle (pool {}). Discarding cycle.", r, edge_arc.pool_info.pool_address);
                            path_valid = false;
                            break;
                        }
                        steps.push(ArbitrageStep {
                            from_token,
                            to_token,
                            rate: r,
                            pool_info: Arc::clone(&edge_arc.pool_info),
                            direction: edge_arc.direction,
                        });
                    } else {
                        info!("Could not get rate for step in cycle (pool {}). Discarding cycle.", edge_arc.pool_info.pool_address);
                        path_valid = false;
                        break;
                    }
                }

                if path_valid {
                    let profit_factor = (-total_log_weight).exp();

                    println!("profit factor: {}", profit_factor);
                    if profit_factor > MIN_PROFIT_FACTOR {
                        info!(
                            "Found potential arbitrage path! Factor: {:.6}, Steps: {}",
                            profit_factor, steps.len()
                        );
                        for step in &steps {
                            debug!(
                                 "  Step: {} -> {} via {} ({:?}), Rate: {:.8}",
                                 step.from_token, step.to_token, step.pool_info.pool_address, step.pool_info.dex, step.rate
                             );
                        }
                        arbitrage_paths.push(ArbitragePath { steps, profit_factor });

                        // Mark nodes in this *reported* cycle to prevent duplicates
                        for node_idx in cycle_nodes {
                            nodes_in_reported_cycles.insert(node_idx);
                        }

                    } else {
                        debug!("Detected cycle profit factor {:.6} below threshold {}, ignoring.", profit_factor, MIN_PROFIT_FACTOR);
                    }
                }
            }
            Err(e) => {
                // Log error but continue trying other start nodes
                error!("Error reconstructing cycle starting from node {}: {}", start_node_idx, e);
            }
        }
    }

    info!("exec time: {:?}", now.elapsed());
    info!("Finished arbitrage check. Total potential paths found: {}", arbitrage_paths.len());
    Ok(arbitrage_paths)
}

// Threshold for floating point comparisons
const FLOATING_POINT_EPSILON: f64 = 1e-12;

/// Helper function to build the graph representation
fn build_graph(
    pools: &[Arc<StandardizedPoolInfo>],
) -> Result<(HashMap<Pubkey, usize>, Vec<Pubkey>, Vec<Edge>), String> {
    let mut token_to_idx: HashMap<Pubkey, usize> = HashMap::new();
    let mut idx_to_token: Vec<Pubkey> = Vec::new();
    let mut edges: Vec<Edge> = Vec::new();
    let mut token_counter = 0;

    let mut get_token_idx = |token: Pubkey| -> usize {
        *token_to_idx.entry(token).or_insert_with(|| {
            let idx = token_counter;
            idx_to_token.push(token);
            token_counter += 1;
            idx
        })
    };

    for pool_arc in pools {
        let pool = pool_arc.as_ref();
        // if pool.price_t1_per_t0.is_sign_negative() || pool.price_t1_per_t0.is_nan() {
        if pool.price_t1_per_t0.is_sign_negative() {
            warn!("Pool {} has invalid price {}, skipping graph edge.", pool.pool_address, pool.price_t1_per_t0);
            continue;
        }
        let idx0 = get_token_idx(pool.token_mint_0);
        let idx1 = get_token_idx(pool.token_mint_1);

        // Edge: Token0 -> Token1
        if let Some(rate_t0_to_t1) = pool.rate_t0_to_t1() {
            if let Some(rate_f64) = rate_t0_to_t1.to_f64() {
                if rate_f64 > FLOATING_POINT_EPSILON { // Use epsilon here too
                    let weight = -rate_f64.ln();
                    if !weight.is_finite() { // Check for NaN/-Infinity from log(<=0)
                        warn!("Invalid weight calculated T0->T1 for pool {}: rate={}, weight={}", pool.pool_address, rate_f64, weight);
                        continue;
                    }
                    edges.push(Edge { from_node_idx: idx0, to_node_idx: idx1, weight, pool_info: Arc::clone(pool_arc), direction: TradeDirection::T0toT1, });
                }
            }
        }
        // Edge: Token1 -> Token0
        if let Some(rate_t1_to_t0) = pool.rate_t1_to_t0() {
            if let Some(rate_f64) = rate_t1_to_t0.to_f64() {
                if rate_f64 > FLOATING_POINT_EPSILON {
                    let weight = -rate_f64.ln();
                    if !weight.is_finite() {
                        warn!("Invalid weight calculated T1->T0 for pool {}: rate={}, weight={}", pool.pool_address, rate_f64, weight);
                        continue;
                    }
                    edges.push(Edge { from_node_idx: idx1, to_node_idx: idx0, weight, pool_info: Arc::clone(pool_arc), direction: TradeDirection::T1toT0, });
                }
            }
        }
    }
    Ok((token_to_idx, idx_to_token, edges))

}


/// Robust Cycle Reconstruction using predecessor traceback (Revised for Production)
/// Returns the edges forming the cycle in order.
fn reconstruct_cycle_robust(
    start_node_idx: usize,                 // A node known to be affected by a negative cycle
    predecessor: &[Option<(usize, Arc<Edge>)>],
    num_nodes: usize,
) -> Result<Vec<Arc<Edge>>, String> {

    // Trace back V steps first to guarantee landing *inside* the cycle.
    // This handles cases where start_node_idx is reachable *from* a cycle but not *in* it.
    let mut node_in_cycle = start_node_idx;
    for _ in 0..num_nodes {
        match predecessor[node_in_cycle] {
            Some((prev_node, _)) => {
                node_in_cycle = prev_node;
            }
            None => {
                // This node wasn't updated V times, suggesting it might not be
                // reachable from the cycle causing the update, or graph issue.
                // Or it could be the ultimate source with no predecessor.
                return Err(format!("Node {} detected in V-th pass but predecessor trace failed early.", start_node_idx));
            }
        }
    }
    // info!("Guaranteed node in cycle after V steps back: {}", node_in_cycle);


    // Now, trace back from node_in_cycle, recording edges and visited nodes
    // until we revisit node_in_cycle.
    let mut cycle_edges = VecDeque::new(); // Use VecDeque for efficient front insertion
    let mut visited_in_trace = HashSet::new(); // Track nodes visited in *this specific trace*
    let mut current_trace_node = node_in_cycle;


    for _ in 0..=num_nodes { // Iterate max V+1 times to detect cycle reliably
        if !visited_in_trace.insert(current_trace_node) {
            // Node visited again! Found the cycle start within the trace.
            if current_trace_node == node_in_cycle {
                // info!("Cycle found starting and ending at {}", node_in_cycle);
                break; // Cycle detected
            } else {
                // This case (hitting another visited node *before* the guaranteed start)
                // could happen in complex graphs but might indicate an issue or
                // that our initial `node_in_cycle` guess wasn't the cleanest entry.
                // Let's treat it as cycle found for now, starting at the repeated node.
                warn!("Cycle detected by revisiting node {} before guaranteed cycle start {}. Adjusting cycle start.", current_trace_node, node_in_cycle);
                node_in_cycle = current_trace_node; // Adjust the "start" for loop trimming
                break;
            }
        }


        match predecessor[current_trace_node] {
            Some((prev_node, ref edge_arc)) => {
                cycle_edges.push_front(Arc::clone(edge_arc)); // Add edge to front
                current_trace_node = prev_node;
            }
            None => {
                // Should not happen if current_trace_node originated from V step back trace
                return Err(format!(
                    "Cycle reconstruction failed: Predecessor missing for node {} during cycle trace.",
                    current_trace_node
                ));
            }
        }
    }


    // If the loop finished without breaking (i.e., > V iterations), something is wrong.
    if visited_in_trace.len() > num_nodes {
        // This check helps catch infinite loops if predecessor graph has issues
        return Err("Cycle reconstruction failed: Trace exceeded maximum iterations. Predecessor graph issue?".to_string());
    }
    // Check if we actually broke because the cycle was found
    if !visited_in_trace.contains(&node_in_cycle) && !cycle_edges.is_empty() {
        // This implies the loop finished the V+1 iterations without revisiting node_in_cycle
        return Err(format!("Cycle reconstruction failed: Did not loop back to cycle start node {}.", node_in_cycle));
    }


    // Convert VecDeque to Vec
    let final_cycle_edges: Vec<Arc<Edge>> = cycle_edges.into();


    // --- Final Validation ---
    if final_cycle_edges.is_empty() {
        // This might happen if the start_node itself had no predecessor after V steps, maybe isolated?
        debug!("Reconstruction resulted in zero edges for start node {}.", start_node_idx);
        return Ok(Vec::new()); // Return empty vec, not an error
    }

    // Check consistency: last edge's 'to' should match first edge's 'from'
    if let (Some(first_edge), Some(last_edge)) = (final_cycle_edges.first(), final_cycle_edges.last()) {
        if last_edge.to_node_idx != first_edge.from_node_idx {
            error!("Reconstructed cycle edges mismatch: Last edge to ({}) != First edge from ({})", last_edge.to_node_idx, first_edge.from_node_idx);
            // You might want to return Err here for production robustness
            // return Err(anyhow!("Cycle edge connection mismatch"));
            // Or return empty for now
            return Ok(Vec::new());
        }
    } else {
        // Should be unreachable if final_cycle_edges is not empty checked above
        return Err("Internal logic error: Cycle edges list became empty unexpectedly.".to_string());
    }


    // info!("Successfully reconstructed cycle with {} edges starting from node {}", final_cycle_edges.len(), start_node_idx);
    Ok(final_cycle_edges)
}









