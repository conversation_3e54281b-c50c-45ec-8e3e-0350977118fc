use std::io::Read;
use std::mem::swap;
use borsh::{BorshDeserialize, BorshSerialize};
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::compute_budget::ComputeBudgetInstruction;
use solana_sdk::instruction::{AccountMeta, Instruction};
use solana_sdk::pubkey;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::{Keypair, Signer};
use solana_sdk::transaction::Transaction;
use spl_associated_token_account::get_associated_token_address_with_program_id;
use crate::solana::dex_integrators::BIN_ARRAY;
use crate::solana::dex_integrators::meteora::model::LbPair;

#[derive(Debug)]
pub struct SwapExactInParams {
    /// Address of the liquidity pair.
    pub lb_pair: Pubkey,
    /// Amount of token to be sell.
    pub amount_in: f64,
    /// Buy direction. true = buy token Y, false = buy token X.
    pub swap_for_y: bool,
}
pub const SWAP2_IX_ACCOUNTS_LEN: usize = 16;

#[derive(Copy, Clone, Debug, PartialEq)]
pub struct Swap2Keys {
    pub lb_pair: Pubkey,
    pub bin_array_bitmap_extension: Pubkey,
    pub reserve_x: Pubkey,
    pub reserve_y: Pubkey,
    pub user_token_in: Pubkey,
    pub user_token_out: Pubkey,
    pub token_x_mint: Pubkey,
    pub token_y_mint: Pubkey,
    pub oracle: Pubkey,
    pub host_fee_in: Pubkey,
    pub user: Pubkey,
    pub token_x_program: Pubkey,
    pub token_y_program: Pubkey,
    pub memo_program: Pubkey,
    pub event_authority: Pubkey,
    pub program: Pubkey,
}

impl From<Swap2Keys> for [AccountMeta; SWAP2_IX_ACCOUNTS_LEN] {
    fn from(keys: Swap2Keys) -> Self {
        [
            AccountMeta {
                pubkey: keys.lb_pair,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.bin_array_bitmap_extension,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.reserve_x,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.reserve_y,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.user_token_in,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.user_token_out,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.token_x_mint,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.token_y_mint,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.oracle,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.host_fee_in,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.user,
                is_signer: true,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.token_x_program,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.token_y_program,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.memo_program,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.event_authority,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.program,
                is_signer: false,
                is_writable: false,
            },
        ]
    }
}
impl From<[Pubkey; SWAP2_IX_ACCOUNTS_LEN]> for Swap2Keys {
    fn from(pubkeys: [Pubkey; SWAP2_IX_ACCOUNTS_LEN]) -> Self {
        Self {
            lb_pair: pubkeys[0],
            bin_array_bitmap_extension: pubkeys[1],
            reserve_x: pubkeys[2],
            reserve_y: pubkeys[3],
            user_token_in: pubkeys[4],
            user_token_out: pubkeys[5],
            token_x_mint: pubkeys[6],
            token_y_mint: pubkeys[7],
            oracle: pubkeys[8],
            host_fee_in: pubkeys[9],
            user: pubkeys[10],
            token_x_program: pubkeys[11],
            token_y_program: pubkeys[12],
            memo_program: pubkeys[13],
            event_authority: pubkeys[14],
            program: pubkeys[15],
        }
    }
}

pub fn derive_event_authority_pda() -> (Pubkey, u8) {
    Pubkey::find_program_address(&[b"__event_authority"], &pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"))
}

pub const BIN_ARRAY_BITMAP_SEED: &[u8] = b"bitmap";
pub fn derive_bin_array_bitmap_extension(lb_pair: Pubkey) -> (Pubkey, u8) {
    Pubkey::find_program_address(
        &[BIN_ARRAY_BITMAP_SEED, lb_pair.as_ref()],
        &pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"),
    )
}

#[repr(C)]
#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq, Copy)]
pub struct BinArrayBitmapExtension {
    pub lb_pair: Pubkey,
    pub positive_bin_array_bitmap: [[u64; 8]; 12],
    pub negative_bin_array_bitmap: [[u64; 8]; 12],
}
#[derive(Clone, Debug, PartialEq)]
pub struct BinArrayBitmapExtensionAccount(pub BinArrayBitmapExtension);

pub const BIN_ARRAY_BITMAP_EXTENSION_ACCOUNT_DISCM: [u8; 8] = [80,111,124,113,55,237,18,5];

impl BinArrayBitmapExtensionAccount {
    pub fn deserialize(buf: &[u8]) -> std::io::Result<Self> {
        use std::io::Read;
        let mut reader = buf;
        let mut maybe_discm = [0u8; 8];
        reader.read_exact(&mut maybe_discm)?;
        if maybe_discm != BIN_ARRAY_BITMAP_EXTENSION_ACCOUNT_DISCM {
            return Err(
                std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!(
                        "discm does not match. Expected: {:?}. Received: {:?}",
                        BIN_ARRAY_BITMAP_EXTENSION_ACCOUNT_DISCM, maybe_discm
                    ),
                ),
            );
        }
        Ok(Self(BinArrayBitmapExtension::deserialize(&mut reader)?))
    }
    pub fn serialize<W: std::io::Write>(&self, mut writer: W) -> std::io::Result<()> {
        writer.write_all(&BIN_ARRAY_BITMAP_EXTENSION_ACCOUNT_DISCM)?;
        self.0.serialize(&mut writer)
    }
    pub fn try_to_vec(&self) -> std::io::Result<Vec<u8>> {
        let mut data = Vec::new();
        self.serialize(&mut data)?;
        Ok(data)
    }
}

#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq)]
pub struct RemainingAccountsSlice {
    pub accounts_type: AccountsType,
    pub length: u8,
}
#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq)]
pub struct RemainingAccountsInfo {
    pub slices: Vec<RemainingAccountsSlice>,
}

#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq)]
pub enum AccountsType {
    TransferHookX,
    TransferHookY,
    TransferHookReward,
}

pub const BASIS_POINT_MAX: i32 = 10000;


pub const SWAP2_IX_DISCM: [u8; 8] = [65, 75, 63, 76, 235, 91, 91, 136];
#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
pub struct Swap2IxArgs {
    pub amount_in: u64,
    pub min_amount_out: u64,
    pub remaining_accounts_info: RemainingAccountsInfo,
}
#[derive(Clone, Debug, PartialEq)]
pub struct Swap2IxData(pub Swap2IxArgs);
impl From<Swap2IxArgs> for Swap2IxData {
    fn from(args: Swap2IxArgs) -> Self {
        Self(args)
    }
}
impl Swap2IxData {
    pub fn deserialize(buf: &[u8]) -> std::io::Result<Self> {
        let mut reader = buf;
        let mut maybe_discm = [0u8; 8];
        reader.read_exact(&mut maybe_discm)?;
        if maybe_discm != SWAP2_IX_DISCM {
            return Err(
                std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!(
                        "discm does not match. Expected: {:?}. Received: {:?}",
                        SWAP2_IX_DISCM, maybe_discm
                    ),
                ),
            );
        }
        Ok(Self(Swap2IxArgs::deserialize(&mut reader)?))
    }
    pub fn serialize<W: std::io::Write>(&self, mut writer: W) -> std::io::Result<()> {
        writer.write_all(&SWAP2_IX_DISCM)?;
        self.0.serialize(&mut writer)
    }
    pub fn try_to_vec(&self) -> std::io::Result<Vec<u8>> {
        let mut data = Vec::new();
        self.serialize(&mut data)?;
        Ok(data)
    }
}



pub async fn build_ix(params: SwapExactInParams, pair: LbPair, price: f64, user: Pubkey, slippage: u64)
    -> Result<(Instruction, u64), String> {
    let SwapExactInParams {
        amount_in,
        lb_pair,
        swap_for_y,
    } = params;

    let program_id = pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo");

    let (event_authority, _bump) = derive_event_authority_pda();

    let token_x_program = pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
    let token_y_program = pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");


    let (user_token_in, user_token_out) = if swap_for_y {
        (
            get_associated_token_address_with_program_id(
                &user,
                &pair.token_x_mint,
                &token_x_program,
            ),
            get_associated_token_address_with_program_id(
                &user,
                &pair.token_y_mint,
                &token_y_program,
            ),
        )
    } else {
        (
            get_associated_token_address_with_program_id(
                &user,
                &pair.token_y_mint,
                &token_y_program,
            ),
            get_associated_token_address_with_program_id(
                &user,
                &pair.token_x_mint,
                &token_x_program,
            ),
        )
    };


    let main_accounts: [AccountMeta; SWAP2_IX_ACCOUNTS_LEN] = Swap2Keys {
        lb_pair,
        bin_array_bitmap_extension: program_id.clone(),
        reserve_x: pair.reserve_x,
        reserve_y: pair.reserve_y,
        token_x_mint: pair.token_x_mint,
        token_y_mint: pair.token_y_mint,
        token_x_program,
        token_y_program,
        user,
        user_token_in,
        user_token_out,
        oracle: pair.oracle,
        host_fee_in: program_id.clone(),
        event_authority,
        program: program_id.clone(),
        memo_program: spl_memo::ID,
    }.into();

    let mut remaining_accounts_info = RemainingAccountsInfo { slices: vec![] };
    let mut remaining_accounts = vec![];

    // 获取 bin 的地址
    let index = if pair.active_id > 0 {
        (pair.active_id as f64 / 70.0).ceil() as i64
    } else {
        (pair.active_id as f64 / 70.0).floor() as i64
    };
    println!("meteora index: {}, active id: {}", index, (pair.active_id as f64 / 70.0).floor());

    let bin_add = Pubkey::find_program_address(
        &[BIN_ARRAY, lb_pair.as_ref(), &index.to_le_bytes()],
        &pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"),
    );
    let bin_array_keys = [bin_add.0];
    remaining_accounts.extend(
        bin_array_keys
            .into_iter()
            .map(|key| AccountMeta::new(key, false)),
    );

    // 50 bps slippage
    let amount_out = if swap_for_y {;
        amount_in * price
    } else {
        amount_in / price
    };
    let amount_out = if swap_for_y {
        amount_out * 10u64.pow(9) as f64
    } else {
        amount_out * 10u64.pow(6) as f64
    };
    let min_amount_out = if swap_for_y {
        amount_out.ceil() as u64 * slippage / BASIS_POINT_MAX as u64
    } else {
        amount_out.ceil() as u64 * slippage / BASIS_POINT_MAX as u64
    };

    let amount = if swap_for_y {
        amount_in * 10u64.pow(6) as f64
    } else {
        amount_in * 10u64.pow(9) as f64
    };
    let data = Swap2IxData(Swap2IxArgs {
        amount_in: amount.ceil() as u64,
        min_amount_out,
        remaining_accounts_info,
    }).try_to_vec().unwrap();

    println!("meteora data amount in: {:?}, min out: {}", amount.ceil() as u64, min_amount_out);

    let accounts = [main_accounts.to_vec(), remaining_accounts].concat();

    println!("meteora main_accounts: {:?}", accounts);


    let swap_ix = Instruction {
        program_id: program_id.clone(),
        accounts,
        data,
    };


    Ok((swap_ix, min_amount_out))
}
