use std::collections::HashMap;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DerivedPrice {
    pub id: String,
    #[serde(rename = "type")]
    pub price_type: String,
    pub price: String,
}

impl DerivedPrice {
    pub fn new(id: String, price_type: String, price: String) -> Self {
        Self {
            id,
            price_type,
            price,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PriceResponse {
    pub data: HashMap<String, DerivedPrice>,
    pub time_taken: f64,
}



#[derive(Debug, Serialize, Deserialize)]
pub enum QuoteResponse {
    Jupiter(QuoteJupResponse),
    Plan(QuotePlanResponse),
}



#[derive(Debug, Serialize, Deserialize)]
pub struct OrderItem {
    pub token: String,
    #[serde(rename = "startAmount")]
    pub start_amount: String,
    #[serde(rename = "endAmount")]
    pub end_amount: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrderInfo {
    pub input: OrderItem,
    pub output: OrderItem,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuoteJupResponse {
    pub routing: String,
    #[serde(rename = "requestId")]
    pub request_id: String,
    #[serde(rename = "quoteId")]
    pub quote_id: String,
    pub maker: String,
    #[serde(rename = "swapMode")]
    pub swap_mode: String,
    #[serde(rename = "orderInfo")]
    pub order_info: OrderInfo,
    pub transaction: Option<String>,
    #[serde(rename = "expireAt")]
    pub expire_at: Option<String>,
    pub swap_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Info {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MostReliableAmmsQuoteReport {
    pub info: Info,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapInfo {
    #[serde(rename = "ammKey")]
    pub amm_key: String,
    pub label: String,
    #[serde(rename = "inputMint")]
    pub input_mint: String,
    #[serde(rename = "outputMint")]
    pub output_mint: String,
    #[serde(rename = "inAmount")]
    pub in_amount: String,
    #[serde(rename = "outAmount")]
    pub out_amount: String,
    #[serde(rename = "feeAmount")]
    pub fee_amount: String,
    #[serde(rename = "feeMint")]
    pub fee_mint: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouterPlan {
    #[serde(rename = "swapInfo")]
    pub swap_info: SwapInfo,
    pub percent: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuotePlanResponse {
    #[serde(rename = "inputMint")]
    pub input_mint: String,
    #[serde(rename = "inAmount")]
    pub in_amount: String,
    pub output_mint: String,
    pub out_amount: String,
    pub other_amount_threshold: String,
    pub swap_mode: String,
    pub slippage_bps: i64,
    pub platform_fee: Option<String>,
    pub price_impact_pct: String,
    pub route_plan: Vec<RouterPlan>,
    pub score_report: Option<String>,
    pub context_slot: i64,
    pub time_taken: f64,
    pub swap_usd_value: String,
    pub simpler_route_used: bool,
    pub most_reliable_amms_quote_report: Option<MostReliableAmmsQuoteReport>,
}




#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuoteSwapRequest {
    pub quote_response: QuotePlanResponse,
    #[serde(rename = "userPublicKey")]
    pub user_public_key: String,
    #[serde(rename = "dynamicComputeUnitLimit")]
    pub dynamic_compute_unit_limit: bool,
    #[serde(rename = "dynamicSlippage")]
    pub dynamic_slippage: bool,
    #[serde(rename = "prioritizationFeeLamports")]
    pub prioritization_fee_lamports: PrioritizationFeeLamports,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PrioritizationFeeLamports {
    pub priority_level_with_max_lamports: PriorityLevelWithMaxLamports,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PriorityLevelWithMaxLamports {
    pub max_lamports: i64,
    pub priority_level: String,
}

impl QuoteSwapRequest {
    pub fn new(
        quote_response: QuotePlanResponse,
        user_public_key: String,
        dynamic_compute_unit_limit: bool,
        dynamic_slippage: bool,
        prioritization_fee_lamports: PrioritizationFeeLamports,
    ) -> Self {
        Self {
            quote_response,
            user_public_key,
            dynamic_compute_unit_limit,
            dynamic_slippage,
            prioritization_fee_lamports,
        }
    }
}

impl PrioritizationFeeLamports {
    pub fn new(max_lamports: i64, priority_level: String) -> Self {
        Self {
            priority_level_with_max_lamports: PriorityLevelWithMaxLamports {
                max_lamports,
                priority_level,
            },
        }
    }
}


#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapResponse {
    pub swap_transaction: String,
    pub last_valid_block_height: u64,
    pub prioritization_fee_lamports: u64,
    pub compute_unit_limit: u64,
}


#[cfg(test)]
mod test {
    use crate::dex::jupiter::model::{QuoteJupResponse, QuotePlanResponse};

    #[test]
    fn test_quote_jup_response_parse() {
        let json_str = r#"{
      "routing": "spot",
      "requestId": "8966657b-9afc-f8a4-4fe6-667e5f533391",
      "quoteId": "59e9dfbb-fd03-5cc9-951a-0a053cdf4929",
      "maker": "AasQTQH9oroodW5vi3uEoDuLyJDVfMz7GWehvisdGmDX",
      "swapMode": "exactIn",
      "orderInfo": {
        "input": {
          "token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
          "startAmount": "********",
          "endAmount": "********"
        },
        "output": {
          "token": "So11111111111111111111111111111111111111112",
          "startAmount": "*********",
          "endAmount": "*********"
        }
      },
      "transaction": null,
      "expireAt": null,
      "swapType": "rfq"
    }"#;
        let quote: QuoteJupResponse = serde_json::from_str(json_str).unwrap();
        assert_eq!(quote.routing, "spot");
    }


    #[test]
    fn test_quote_plan_response_parse() {
        let json_str = r#"{"inputMint":"Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB","inAmount":"*********","outputMint":"9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump","outAmount":"*********","otherAmountThreshold":"*********","swapMode":"ExactIn","slippageBps":10,"platformFee":null,"priceImpactPct":"0.0002890934196823437940925504","routePlan":[{"swapInfo":{"ammKey":"8xxez7dC4JvXAxov6cJXXzathPXaz8xGWRj18R9yFAtA","label":"Phoenix","inputMint":"Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB","outputMint":"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v","inAmount":"*********","outAmount":"*********","feeAmount":"49988","feeMint":"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"},"percent":100},{"swapInfo":{"ammKey":"9yvwfAWcEPCkUftKzFJzy3V2BdcGyTnkdv4VmKKeLHEb","label":"OpenBook V2","inputMint":"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v","outputMint":"9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump","inAmount":"*********","outAmount":"*********","feeAmount":"0","feeMint":"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"},"percent":100}],"scoreReport":null,"contextSlot":332442701,"timeTaken":0.006424868,"swapUsdValue":"499.78079614281176276085308985","simplerRouteUsed":false,"mostReliableAmmsQuoteReport":{"info":{}}}"#;
        let quote: QuotePlanResponse = serde_json::from_str(json_str).unwrap();
        assert_eq!(quote.input_mint, "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB");
    }
}
