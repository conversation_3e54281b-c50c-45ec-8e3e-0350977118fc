use std::collections::HashMap;
use base64::{engine::general_purpose::STANDARD, Engine as _};
use chrono::{SecondsFormat, Utc};
use hmac::{Hmac, Mac};
use serde::{Deserialize, Serialize};
use sha2::Sha256;
use crate::okx::{API_KEY, API_SECRET, BASE_URL};

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapQuoteRequest {
    pub chain_index: String,
    pub chain_id: String,
    pub amount: String,
    pub from_token_address: String,
    pub to_token_address: String,
    pub dex_ids: Option<String>,
    pub direct_route: Option<String>,
    pub price_impact_protection_percentage: Option<String>,
    pub fee_percent: Option<String>,
}


impl SwapQuoteRequest {
    pub fn to_query_string(&self) -> String {
        let mut params = HashMap::new();

        params.insert("chainIndex", self.chain_index.as_str());
        params.insert("chainId", self.chain_id.as_str());
        params.insert("amount", self.amount.as_str());
        params.insert("fromTokenAddress", self.from_token_address.as_str());
        params.insert("toTokenAddress", self.to_token_address.as_str());

        if let Some(ref dex_ids) = self.dex_ids {
            params.insert("dexIds", dex_ids);
        }
        if let Some(ref direct_route) = self.direct_route {
            params.insert("directRoute", direct_route);
        }
        if let Some(ref price_impact_protection_percentage) = self.price_impact_protection_percentage {
            params.insert("priceImpactProtectionPercentage", price_impact_protection_percentage);
        }
        if let Some(ref fee_percent) = self.fee_percent {
            params.insert("feePercent", fee_percent);
        }

        serde_urlencoded::to_string(params).unwrap_or_default()
    }
}


#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapQuoteResponse {
    pub code: String,
    pub msg: String,
    pub data: Vec<SwapQuoteInfo>
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapQuoteInfo {
    pub chain_index: String,
    pub chain_id: String,
    pub dex_router_list: Vec<DexRouter>,
    pub estimate_gas_fee: String,
    pub from_token: TokenInfo,
    pub from_token_amount: String,
    pub price_impact_percentage: String,
    pub quote_compare_list: Vec<QuoteCompare>,
    pub to_token: TokenInfo,
    pub to_token_amount: String,
    pub trade_fee: String,
    pub swap_mode: String
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct QuoteCompare {
    pub dex_name: String,
    pub trade_fee: String,
    pub dex_logo: String,
    pub amount_out: String,
}


#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct DexRouter {
    pub router: String,
    pub router_percent: String,
    pub sub_router_list: Vec<SubRouter>
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SubRouter {
    pub from_token: TokenInfo,
    pub to_token: TokenInfo,
    pub dex_protocol: Vec<DexProtocol>
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct DexProtocol {
    pub dex_name: String,
    pub percent: String
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenInfo {
    pub decimal: String,
    pub is_honey_pot: bool,
    pub tax_rate: String,
    pub token_contract_address: String,
    pub token_symbol: String,
    pub token_unit_price: String
}

type HmacSha256 = Hmac<Sha256>;

pub async fn get_swap_quote(param: SwapQuoteRequest) -> Result<Vec<SwapQuoteInfo>, String> {
    let request_path = "/api/v5/dex/aggregator/quote";
    let client = reqwest::Client::new();

    let query_string = param.to_query_string();
    let url = format!("{}{}?{}", BASE_URL, request_path, query_string);

    let timestamp = Utc::now().to_rfc3339_opts(SecondsFormat::Millis, true);
    let hash_string = format!("{}{}{}?{}", timestamp, "GET", request_path, query_string);

    let mut mac = HmacSha256::new_from_slice(API_SECRET.as_bytes())
        .expect("HMAC can take key of any size");
    mac.update(hash_string.as_bytes());
    let signature_bytes = mac.finalize().into_bytes();

    let signature_base64 = STANDARD.encode(signature_bytes);

    let response = client.get(&url)
        .header("content-type", "application/json")
        .header("OK-ACCESS-KEY", API_KEY)
        .header("OK-ACCESS-PASSPHRASE", "!Tvr4y-4H4-EtxC")
        .header("OK-ACCESS-TIMESTAMP", timestamp)
        .header("OK-ACCESS-SIGN", signature_base64)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("Request failed with status: {}", response.status()));
    }

    let res: SwapQuoteResponse = response.json()
        .await
        .map_err(|e| format!("Failed to parse response: {}", e))?;

    if res.code != "0" {
        Err(format!("Error from API: {} - {}", res.code, res.msg))
    } else {
        Ok(res.data)
    }
}


pub async fn get_support_chains()  {
    let request_path = "/api/v5/dex/aggregator/supported/chain";
    let client = reqwest::Client::new();
    let url = format!("{}{}", BASE_URL, request_path);

    let timestamp = Utc::now().to_rfc3339_opts(SecondsFormat::Millis, true);
    let hash_string = format!("{}{}{}", timestamp, "GET", request_path);

    let mut mac = HmacSha256::new_from_slice(API_SECRET.as_bytes())
        .expect("HMAC can take key of any size");
    mac.update(hash_string.as_bytes());
    let signature_bytes = mac.finalize().into_bytes();

    let signature_base64 = STANDARD.encode(signature_bytes);

    let response = client.get(&url)
        .header("content-type", "application/json")
        .header("OK-ACCESS-KEY", API_KEY)
        .header("OK-ACCESS-PASSPHRASE", "!Tvr4y-4H4-EtxC")
        .header("OK-ACCESS-TIMESTAMP", timestamp)
        .header("OK-ACCESS-SIGN", signature_base64)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e)).unwrap();

    if !response.status().is_success() {
        println!("{}", format!("Request failed with status: {}", response.status()));
    }

    let text = response.text()
        .await
        .map_err(|e| format!("Failed to read response text: {}", e)).unwrap();

    println!("Response Text: {}", text);
}
