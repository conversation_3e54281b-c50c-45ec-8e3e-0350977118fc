use std::ops::Div;
use std::str::FromStr;
use std::sync::Arc;
use borsh::BorshDeserialize;
use rust_decimal::{Decimal, MathematicalOps};
use rust_decimal::prelude::ToPrimitive;
use rust_decimal_macros::dec;
use solana_client::rpc_client::RpcClient;
use tracing::{error};
use crate::solana::dex_integrators::{ANCHOR_DISCRIMINATOR_SIZE, BATCH_SIZE};
use crate::solana::dex_integrators::meteora::model::{DllmPoolInfo, LbPair};
use crate::solana::dex_integrators::meteora::pools;


pub async fn get_meteora_dlmm_pool(rpc: Arc<RpcClient>) -> Vec<DllmPoolInfo> {
    let chunks = pools::METEORA_POOLS.chunks(BATCH_SIZE);
    let mut pools_info: Vec<DllmPoolInfo> = Vec::new();
    
    for ck in chunks {
        let result = rpc.get_multiple_accounts(&ck.iter().map(|x| x.pool_id).collect::<Vec<_>>());
        match result {
            Ok(accounts_data) => {
                for (i, account_data) in accounts_data.iter().enumerate() {
                    if let Some(account) = account_data {
                        let pool_info = LbPair::deserialize(&mut &account.data[ANCHOR_DISCRIMINATOR_SIZE..]).unwrap();

                        let token_x_decimal = ck.get(i).unwrap().token_x_decimals;
                        let token_y_decimal = ck.get(i).unwrap().token_y_decimals;
                        let price = process_meteora_dlmm(pool_info, token_x_decimal, token_y_decimal);

                        match price {
                            Ok(p) => {
                                let pool_id = ck.get(i).unwrap().pool_id;
                                pools_info.push(DllmPoolInfo {
                                    pool_id,
                                    current_price: p,
                                    pool_info,
                                    token_x_decimal,
                                    token_y_decimal,
                                });
                            }
                            Err(err) => {
                                error!("Error processing pool {}", err);
                            }
                        }
                    }
                }
            }
            Err(e) => {
                error!("Failed to fetch account chunk: {}", e);
            }
        }
    }

    pools_info
}

pub fn process_meteora_dlmm(lb_pair: LbPair, token_x_decimal: u8, token_y_decimal: u8) -> Result<f64, String> {
    let bin_step = Decimal::from_str(&lb_pair.bin_step.to_string());
    let active_id = lb_pair.active_id;
    
    if bin_step.is_ok() {
        let bin_step = bin_step.unwrap();
        let base_factor = dec!(10000);
        
        println!("LBPair: bin step {:?}, base factor: {}, active id: {}", bin_step, base_factor, active_id);

        let base = dec!(1) + bin_step.div(base_factor);
        let price = base.powi(active_id as i64);
        

        return if token_x_decimal >= token_y_decimal {
            let price = price * Decimal::from(
                Decimal::from_str("10").unwrap().powi((token_x_decimal - token_y_decimal) as i64));

            let price = price.to_f64().unwrap();
            Ok(price)
        } else {
            let price = price / Decimal::from(
                Decimal::from_str("10").unwrap().powi((token_y_decimal - token_x_decimal) as i64));

            let price = price.to_f64().unwrap();
            Ok(price)
        }
    }
    
    Err("Failed to parse bin step".to_string())
}