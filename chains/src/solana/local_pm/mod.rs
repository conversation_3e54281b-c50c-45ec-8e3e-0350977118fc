use crate::solana::dex_integrators::raydium::clmm::RawPoolData;
use crate::solana::dex_integrators::raydium::model::PoolState;
use borsh::BorshDeserialize;
use rust_decimal::{Decimal, MathematicalOps};
use rust_decimal_macros::dec;
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::native_token::LAMPORTS_PER_SOL;
use solana_sdk::pubkey;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::Keypair;
use solana_sdk::signer::Signer;
use solana_sdk::system_instruction::{create_account, transfer};
use solana_sdk::system_program::ID as SYSTEM_PROGRAM_ID;
use solana_sdk::transaction::Transaction;
use spl_associated_token_account::get_associated_token_address;
use spl_associated_token_account::instruction::create_associated_token_account_idempotent;
use spl_memo::build_memo;
use spl_token::instruction::initialize_mint2;
use spl_token::solana_program::program_pack::Pack;
use spl_token::state::Mint;
use std::str::FromStr;


// address: E3zJT9DMmeF8hW52D2WCtmbpnmXVbQxBWtrPQiFkFE9f,
// key: 5mb92fHi7dVbeTjvHeXQ8fsT4DZFYX6zZfWoYjLSA8qykzHPbmSn9LrWT5rzkosK5dgkXf3BUtPd8iykTUXW5pos


// address: HZFZ7vFfdRvCmtCcNuV8ZicTZqQVSBrosZwsEtAr14z,
// private: 62gHt54rGm7PYcZYXrxb61T4tnDEM3RP4QDKxM6rhRsaUvHMwRiRkHGLxwv2cH8cYMAZLbzyncHYUgWGYGfmtcML


// Token
// 5fLdytd5czcQmvEN6eVJ8Fzhzhy8kQJmpVY9tbEFtygv

pub fn get_rpc() -> RpcClient {
    RpcClient::new_with_commitment(
        String::from("http://**************:8899"),
        CommitmentConfig::confirmed(),
    )
}


pub fn local_sol_airdrop() {
    let pk = "5mb92fHi7dVbeTjvHeXQ8fsT4DZFYX6zZfWoYjLSA8qykzHPbmSn9LrWT5rzkosK5dgkXf3BUtPd8iykTUXW5pos";
    let keypair = Keypair::from_base58_string(pk);
    let rpc_client = get_rpc();

    println!("keypair: {:?}, private: {}", keypair.pubkey(), keypair.to_base58_string());

    let balance = rpc_client.get_balance(&keypair.pubkey()).unwrap();
    println!("Balance: {}", balance);

    let signature = rpc_client.request_airdrop(&keypair.pubkey(), LAMPORTS_PER_SOL).unwrap();

    println!("Airdrop signature: {}", signature);

    // Wait for the transaction to be confirmed
    let mut confirmation = rpc_client.confirm_transaction(&signature).unwrap();

    while !confirmation {
        println!("Waiting for confirmation...");
        std::thread::sleep(std::time::Duration::from_secs(5));
        confirmation = rpc_client.confirm_transaction(&signature).unwrap();
    }

    let balance = rpc_client.get_balance(&keypair.pubkey()).unwrap();
    println!("New balance: {}", balance);
}


pub fn send_token() {
    let sender = Keypair::from_base58_string("5mb92fHi7dVbeTjvHeXQ8fsT4DZFYX6zZfWoYjLSA8qykzHPbmSn9LrWT5rzkosK5dgkXf3BUtPd8iykTUXW5pos");

    let rec = pubkey!("HZFZ7vFfdRvCmtCcNuV8ZicTZqQVSBrosZwsEtAr14z");

    let rpc_client = get_rpc();

    let transfer_ix = transfer(&sender.pubkey(), &rec, LAMPORTS_PER_SOL / 10);
    let recent_blockhash = rpc_client.get_latest_blockhash().unwrap();

    let mut tran = Transaction::new_with_payer(&[transfer_ix], Some(&sender.pubkey()));
    tran.sign(&[&sender], recent_blockhash);

    match rpc_client.send_and_confirm_transaction(&tran) {
        Ok(signature) => {
            println!("Transaction successful with signature: {}", signature);
        }
        Err(e) => {
            println!("Transaction failed: {:?}", e);
        }
    }
}


pub fn calculate_cost() {
    let rpc_client = get_rpc();

    let sender = Keypair::from_base58_string("5mb92fHi7dVbeTjvHeXQ8fsT4DZFYX6zZfWoYjLSA8qykzHPbmSn9LrWT5rzkosK5dgkXf3BUtPd8iykTUXW5pos");
    let memo = String::from("message to be logged");

    let memo_ix = build_memo(memo.as_bytes(), &[&sender.pubkey()]);
    let recent_blockhash = rpc_client.get_latest_blockhash().unwrap();

    let rec = pubkey!("HZFZ7vFfdRvCmtCcNuV8ZicTZqQVSBrosZwsEtAr14z");
    let transfer_ix = transfer(&sender.pubkey(), &rec, LAMPORTS_PER_SOL / 10 * 2);

    let mut tran = Transaction::new_with_payer(&[memo_ix, transfer_ix], Some(&sender.pubkey()));
    tran.sign(&[&sender], recent_blockhash);


    let transaction_signature = rpc_client
        .request_airdrop(&sender.pubkey(), 5 * LAMPORTS_PER_SOL).unwrap();
    loop {
        if rpc_client.confirm_transaction(&transaction_signature).unwrap() {
            break;
        }
    }

    let sim_res = rpc_client.simulate_transaction(&tran).unwrap();

    let units_consumed = sim_res
        .value
        .units_consumed
        .expect("couldn't estimate CUs used");

    println!("Transaction estimated to consumed {units_consumed} compute units");

    let tx_cost = rpc_client.get_fee_for_message(tran.message()).unwrap();

    println!("Transaction cost: {}", tx_cost);

    match rpc_client.send_and_confirm_transaction(&tran) {
        Ok(signature) => {
            println!("Transaction successful with signature: {}", signature);
        }
        Err(e) => {
            println!("Transaction failed: {:?}", e);
        }
    }

}

fn create_sol_account() {
    let rpc_client = get_rpc();
    let new_account = Keypair::new();

    let sender = Keypair::from_base58_string("5mb92fHi7dVbeTjvHeXQ8fsT4DZFYX6zZfWoYjLSA8qykzHPbmSn9LrWT5rzkosK5dgkXf3BUtPd8iykTUXW5pos");
    let data_len = 0;
    let rent_exemption_amount = rpc_client
        .get_minimum_balance_for_rent_exemption(data_len)
        .unwrap();

    let create_acc_ix = create_account(&sender.pubkey(), &new_account.pubkey(),
        rent_exemption_amount, data_len as u64, &SYSTEM_PROGRAM_ID);


    let mut tran = Transaction::new_with_payer(&[create_acc_ix], Some(&sender.pubkey()));
    tran.sign(&[&sender, &new_account], rpc_client.get_latest_blockhash().unwrap());
    match rpc_client.send_and_confirm_transaction(&tran) {
        Ok(signature) => {
            println!("Transaction successful with signature: {}", signature);
        }
        Err(e) => {
            println!("Transaction failed: {:?}", e);
        }
    }
}


fn create_mint_account() {
    let addr = Keypair::new();

    println!("mint address: {:?}", addr.pubkey());

    let authority = Keypair::from_base58_string("5mb92fHi7dVbeTjvHeXQ8fsT4DZFYX6zZfWoYjLSA8qykzHPbmSn9LrWT5rzkosK5dgkXf3BUtPd8iykTUXW5pos");

    let rpc_client = get_rpc();
    let mint_account_len = Mint::LEN;
    let rent_exemption_amount = rpc_client
        .get_minimum_balance_for_rent_exemption(mint_account_len)
        .unwrap();

    let create_mint_ix = create_account(
        &authority.pubkey(),
        &addr.pubkey(),
        rent_exemption_amount,
        mint_account_len as u64,
        &spl_token::id(),
    );

    let init_mit_ix = initialize_mint2(
        &spl_token::id(),
        &addr.pubkey(),
        &authority.pubkey(),
        None,
        9,
    ).unwrap();

    let mut tran = Transaction::new_with_payer(&[create_mint_ix, init_mit_ix], Some(&authority.pubkey()));
    tran.sign(&[&authority, &addr], rpc_client.get_latest_blockhash().unwrap());
    match rpc_client.send_and_confirm_transaction(&tran) {
        Ok(signature) => {
            println!("Transaction successful with signature: {}", signature);
        }
        Err(e) => {
            println!("Transaction failed: {:?}", e);
        }
    }
}


fn get_token_mint() {
    let rpc_client = get_rpc();
    let mint_address = pubkey!("5fLdytd5czcQmvEN6eVJ8Fzhzhy8kQJmpVY9tbEFtygv");
    let mint_account = rpc_client.get_account(&mint_address).unwrap();
    let mint = Mint::unpack(&mint_account.data).unwrap();

    println!("Mint address: {:?}", mint_address);
    println!("Mint authority: {:?}", mint.mint_authority);
    println!("Supply: {:?}", mint.supply);
    println!("Decimals: {:?}", mint.decimals);
}

fn create_token_account(for_address: Pubkey) {
    let rpc_client = get_rpc();
    let mint_address = pubkey!("5fLdytd5czcQmvEN6eVJ8Fzhzhy8kQJmpVY9tbEFtygv");

    let authority = Keypair::from_base58_string("5mb92fHi7dVbeTjvHeXQ8fsT4DZFYX6zZfWoYjLSA8qykzHPbmSn9LrWT5rzkosK5dgkXf3BUtPd8iykTUXW5pos");

    let create_token_account_ix = create_associated_token_account_idempotent(
        &authority.pubkey(),
        &for_address,
        &mint_address,
        &spl_token::id(),
    );

    let mut tran = Transaction::new_with_payer(&[create_token_account_ix], Some(&authority.pubkey()));
    tran.sign(&[&authority], rpc_client.get_latest_blockhash().unwrap());
    match rpc_client.send_and_confirm_transaction(&tran) {
        Ok(signature) => {
            println!("Transaction successful with signature: {}", signature);
        }
        Err(e) => {
            println!("Transaction failed: {:?}", e);
        }
    }
}

fn mint_token() {
    let rpc_client = get_rpc();
    let mint_address = pubkey!("5fLdytd5czcQmvEN6eVJ8Fzhzhy8kQJmpVY9tbEFtygv");

    let to_address = pubkey!("HZFZ7vFfdRvCmtCcNuV8ZicTZqQVSBrosZwsEtAr14z");
    let authority = Keypair::from_base58_string("5mb92fHi7dVbeTjvHeXQ8fsT4DZFYX6zZfWoYjLSA8qykzHPbmSn9LrWT5rzkosK5dgkXf3BUtPd8iykTUXW5pos");

    let ata_account = get_associated_token_address(&to_address, &mint_address);
    let mut ata_balance = rpc_client.get_token_account_balance(&ata_account);
    if ata_balance.is_err() {
        create_token_account(to_address);
        ata_balance = rpc_client.get_token_account_balance(&ata_account);
    }
    println!("ATA balance: {:?}", ata_balance);

    let mint_decimals = ata_balance.unwrap().decimals;
    let mint_amount = 105 * 10_u64.pow(mint_decimals as u32);

    let mint_ix = spl_token::instruction::mint_to(
        &spl_token::id(),
        &mint_address,
        &ata_account,
        &to_address,
        &[&authority.pubkey()],
        mint_amount,
    ).unwrap();

    let mut tran = Transaction::new_with_payer(&[mint_ix], Some(&authority.pubkey()));
    tran.sign(&[&authority], rpc_client.get_latest_blockhash().unwrap());
    match rpc_client.send_and_confirm_transaction(&tran) {
        Ok(signature) => {
            println!("Transaction successful with signature: {}", signature);
        }
        Err(e) => {
            println!("Transaction failed: {:?}", e);
        }
    }
}







fn calculate_clmm_price(
    sqrt_price_x64: u128,
    decimals0: u8,
    decimals1: u8,
) -> Result<Decimal, String> {
    if sqrt_price_x64 == 0 {
        return Err("sqrt_price_x64 cannot be zero".to_string());
    }

    // Convert sqrt_price_x64 (U64.64 fixed point) to Decimal
    // sqrt_price_x64 / 2^64
    let sqrt_price_x64_dec = Decimal::from_str(&sqrt_price_x64.to_string()).unwrap();
    let two_pow_64 = Decimal::from(2u128).powu(64); // 2^64
    let sqrt_price = sqrt_price_x64_dec / two_pow_64;

    // Price = (sqrt_price)^2
    let price_ratio = sqrt_price * sqrt_price;

    // Adjust for decimals: price_ratio * 10^(decimals0 - decimals1)
    let decimals_diff = decimals0 as i32 - decimals1 as i32;
    let decimal_adj_factor = dec!(10).powi(decimals_diff.abs().into()); // 10^|diff|

    let final_price = if decimals_diff >= 0 {
        price_ratio * decimal_adj_factor
    } else {
        price_ratio / decimal_adj_factor
    };

    // Ensure the price is not negative (shouldn't happen with squares)
    if final_price.is_sign_negative() {
        return Err("Calculated negative price, sqrt_price_x64 was likely invalid".to_string());
    }

    Ok(final_price)
}




async fn get_multiple_accounts_batched(
    client: &RpcClient,
    pubkeys: &[Pubkey],
) -> Result<Vec<RawPoolData>, String> {
    let mut res: Vec<RawPoolData> = Vec::new();

    let vec = client.get_multiple_accounts(&pubkeys).unwrap();

    // 根据 pubkeys 和 返回的 vec 进行映射
    for (i, account) in vec.iter().enumerate() {
        if let Some(account) = account {
            let pool_data = PoolState::deserialize(&mut &account.data[8..]).unwrap();
            res.push(RawPoolData {
                address: pubkeys[i],
                account_data: pool_data,
            });
        }
    }

    Ok(res)
}



