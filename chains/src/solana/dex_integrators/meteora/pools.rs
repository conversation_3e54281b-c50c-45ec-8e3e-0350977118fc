use solana_sdk::pubkey;
use solana_sdk::pubkey::Pubkey;

pub struct PoolAccountInfo {
    pub token_x_decimals: u8,
    pub token_y_decimals: u8,
    pub pool_id: Pubkey,
}

pub const METEORA_POOLS: [PoolAccountInfo; 5] = [
    PoolAccountInfo {
        token_x_decimals: 6,
        token_y_decimals: 9,
        pool_id: pubkey!("FEKBRuXEoCSKHjDHHXnrBv76eckpsyKhpHnenW3JPEPM")
    },
    PoolAccountInfo {
        token_x_decimals: 6,
        token_y_decimals: 9,
        pool_id: pubkey!("9KKGQ2UCXuE4TwFvnWU8NyBzmPYrNtXx9ZoVZh49fc9M")
    },
    PoolAccountInfo {
        token_x_decimals: 6,
        token_y_decimals: 9,
        pool_id: pubkey!("A9fwXurM9CDmh4b1q6kWY4Qn1YnwNVPRQTr4gfR4UFPG")
    },
    PoolAccountInfo {
        token_x_decimals: 6,
        token_y_decimals: 9,
        pool_id: pubkey!("FgpyPTyrMx9NjUNAnkfc587ZYWoRNqHZAmFLynH2ACjW")
    },
    PoolAccountInfo {
        token_x_decimals: 6,
        token_y_decimals: 9,
        pool_id: pubkey!("8ufvt4KWzNxiFqhr2HbgRUfxbWAnuKuBL5NMch6Yopc")
    }
];