use chrono::{SecondsFormat, Utc};
use hmac::{Hmac, Mac};
use sha2::Sha256;
use base64::{engine::general_purpose::STANDARD, Engine as _};
use serde::{Deserialize, Serialize};
use crate::okx::{API_KEY, API_SECRET, BASE_URL};

type HmacSha256 = Hmac<Sha256>;


#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PriceInfo {
    pub chain_index: String,
    pub token_contract_address: String,
    pub time: String,
    pub price: String,
    pub market_cap: String,
    #[serde(rename = "priceChange5M")]
    pub price_change_5m: String,
    #[serde(rename = "priceChange1H")]
    pub price_change_1h: String,
    #[serde(rename = "priceChange4H")]
    pub price_change_4h: String,
    #[serde(rename = "priceChange24H")]
    pub price_change_24h: String,
    #[serde(rename = "volume5M")]
    pub volume_5m: String,
    #[serde(rename = "volume1H")]
    pub volume_1h: String,
    #[serde(rename = "volume4H")]
    pub volume_4h: String,
    #[serde(rename = "volume24H")]
    pub volume_24h: String,
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
struct PriceResponse {
    pub code: String,
    pub msg: String,
    pub data: Vec<PriceInfo>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct TokenPriceRequest {
    pub chain_index: String,
    pub token_contract_address: String,
}


// 批量获取价格
pub async fn get_batch_quote_prices(tokens: Vec<TokenPriceRequest>) 
    -> Result<Vec<PriceInfo>, String> {
    let request_path = "/api/v5/dex/market/price-info";
    let client = reqwest::Client::new();
    let url = format!("{}{}", BASE_URL, request_path);

    if tokens.is_empty() {
        return Err("Token address list is empty".to_string());
    }

    let body_payload = serde_json::json!(tokens);
    let body_str = serde_json::to_string(&body_payload)
        .map_err(|e| format!("Failed to serialize request body: {}", e))?;;

    let timestamp = Utc::now().to_rfc3339_opts(SecondsFormat::Millis, true);
    let hash_string = format!("{}{}{}{}", timestamp, "POST", request_path, body_str);

    let mut mac = HmacSha256::new_from_slice(API_SECRET.as_bytes())
        .expect("HMAC can take key of any size");
    mac.update(hash_string.as_bytes());
    let signature_bytes = mac.finalize().into_bytes();

    let signature_base64 = STANDARD.encode(signature_bytes);

    let response = client.post(&url)
        .body(body_str)
        .header("content-type", "application/json")
        .header("OK-ACCESS-KEY", API_KEY)
        .header("OK-ACCESS-PASSPHRASE", "!Tvr4y-4H4-EtxC")
        .header("OK-ACCESS-TIMESTAMP", timestamp)
        .header("OK-ACCESS-SIGN", signature_base64)
        .send()
        .await
        .map_err(|e| e.to_string())?;

    if !response.status().is_success() {
        return Err(format!("Failed to fetch prices: {}", response.status()));
    }

    // let data: serde_json::Value = response.json().await.map_err(|e| e.to_string())?;

    let res: PriceResponse = response.json().await
        .map_err(|e| format!("Failed to parse response JSON: {}", e))?;
    
    
    if res.code != "0" {
        Err(format!("Error from API: {} - {}", res.code, res.msg))
    } else {
        Ok(res.data)
    }
}



// Test
#[cfg(test)]
mod tests {
    use chrono::{SecondsFormat, Utc};

    #[tokio::test]
    async fn test_get_batch_quote_prices() {
        let timestamp = Utc::now().to_rfc3339_opts(SecondsFormat::Millis, true);
        println!("Timestamp: {}", timestamp);
    }
    }