use std::sync::Mutex;
use std::time::{Duration, Instant};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use tracing::error;

const APP_ID: &str = "cli_a77dd26cd236d00e";
const APP_SECRET: &str = "G0ofXQRIsZfLXKGH1rASMdC80dpmtP8g";
pub const OPEN_ID: &str = "ou_5fd75220b4ab30d7b14fa4b0393c64ef";
// --- 安全边际时间 ---
// 提前 N 秒刷新 token，以防止 token 在使用时刚好过期
const EXPIRY_BUFFER: Duration = Duration::from_secs(300);

#[derive(Debug, Clone)]
struct CachedToken {
    token: String,
    expires_at: Instant,
}

static TOKEN_CACHE: Lazy<Mutex<Option<CachedToken>>> = Lazy::new(|| Mutex::new(None));

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
struct AccessTokenResponse {
    pub code: i64,
    pub msg: String,
    #[serde(rename = "tenant_access_token")]
    pub tenant_access_token: String,
    pub expire: u64,
}


pub async fn get_access_token(
    app_id: &str,
    app_secret: &str,
) -> Result<String, String> {
    // --- 1. 检查缓存 ---
    {
        // 创建一个作用域，锁保护器会在作用域结束时自动释放
        let mut cache_guard = TOKEN_CACHE.lock()
            .map_err(|_| "Failed to acquire cache lock".to_string())?;
        if let Some(cached) = cache_guard.as_ref() {
            if Instant::now() < cached.expires_at {
                return Ok(cached.token.clone());
            } else {
                *cache_guard = None;
            }
        }
    }


    let client = reqwest::Client::new();
    let response = client
        .post("https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal")
        .header("Content-Type", "application/json; charset=utf-8")
        .json(&serde_json::json!({
            "app_id": app_id,
            "app_secret": app_secret,
        }))
        .send()
        .await.map_err(|e| {
            format!("Failed to get access token: {}", e)
        })?;

    if response.status().is_success() {
        let token_response: AccessTokenResponse = response.json().await
            .map_err(|e| format!("Failed to parse token response JSON: {}", e))?;

        let token = token_response.tenant_access_token;
        let expires_in = token_response.expire;

        let expires_at = Instant::now()
            .checked_add(Duration::from_secs(expires_in))
            .ok_or("Failed to calculate expiry time (overflow)")?
            .checked_sub(EXPIRY_BUFFER)
            .unwrap_or_else(Instant::now);

        { // 再次获取锁以更新缓存
            let mut cache_guard = TOKEN_CACHE.lock()
                .map_err(|_| "Failed to acquire cache lock for writing".to_string())?;
            let new_cached_token = CachedToken {
                token: token.clone(), // 存储 token 的克隆
                expires_at,
            };
            println!("Caching new token, expires at: {:?}", expires_at); // 日志
            *cache_guard = Some(new_cached_token);
        }

        Ok(token)

    } else {
        Err("Failed to get access token".to_string())
    }
}

#[derive(Serialize, Deserialize)]
struct Message {
    pub receive_id: String,
    pub msg_type: String,
    pub content: String,
    pub uuid: String,
}

pub async fn send_message(
    message: &str,
    open_id: &str,
) -> Result<(), String> {
    let token = get_access_token(APP_ID, APP_SECRET).await?;

    let message_request = Message {
        receive_id: open_id.to_string(),
        msg_type: "text".to_string(),
        content: format!(r#"{{"text": "{}"}}"#, message),
        uuid: "".to_string(),
    };
    let message_request = serde_json::to_string(&message_request)
        .map_err(|e| format!("Failed to serialize message: {}", e))?;

    let client = reqwest::Client::new();
    let response = client
        .post("https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=open_id")
        .header("Content-Type", "application/json; charset=utf-8")
        .header("Authorization",format!("Bearer {}", token))
        .body(message_request)
        .send()
        .await.map_err(|e| {
            format!("Failed to send message: {}", e)
        })?;

    if response.status().is_success() {
        Ok(())
    } else {
        error!("Failed to send message: {:?}", message);
        error!("Failed to send message: {:?}", response.text().await);
        Err("Failed to send message".to_string())
    }
}



#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_access_token() {
        let token = get_access_token(APP_ID, APP_SECRET).await;
        assert!(token.is_ok());
        println!("Access Token: {:?}", token.unwrap());
    }

    #[tokio::test]
    async fn test_send_message() {
        let result = send_message("Hello, world! \\n\\n 时间", OPEN_ID).await;
        assert!(result.is_ok());
    }
}
