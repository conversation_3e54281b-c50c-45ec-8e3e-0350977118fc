use std::str::FromStr;
use std::sync::Arc;
use borsh::BorshDeserialize;
use solana_account_decoder::UiAccountEncoding;
use solana_client::rpc_client::RpcClient;
use solana_client::rpc_config::{RpcAccountInfoConfig, RpcProgramAccountsConfig};
use solana_client::rpc_filter::{Memcmp, RpcFilterType};
use solana_sdk::account::ReadableAccount;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::pubkey;
use solana_sdk::pubkey::Pubkey;
use spl_token::solana_program::program_pack::Pack;
use tracing::error;
use crate::solana::dex_integrators::pump::{PumpPool, PumpPoolInfo};

const PUMP_AMM_PROGRAM_ID: Pubkey = pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
const WSOL_TOKEN_ACCOUNT: Pubkey = pubkey!("So11111111111111111111111111111111111111112");

fn get_rpc_client(rpc_url: &str) -> RpcClient {
    RpcClient::new_with_commitment(
        rpc_url,
        CommitmentConfig::confirmed(),
    )
}

pub fn get_pools_with_base_mint(mint: &Pubkey, rpc_url: &str) {
    let rpc_client = get_rpc_client(rpc_url);

    let mint_filter =
        RpcFilterType::Memcmp(Memcmp::new_base58_encoded(43, &mint.to_bytes()));

    let size_filter =
        RpcFilterType::DataSize(211); //

    let account_config = RpcAccountInfoConfig {
        encoding: Some(UiAccountEncoding::Base64),
        ..Default::default()
    };

    let config = RpcProgramAccountsConfig {
        filters: Some(vec![mint_filter, size_filter]),
        account_config,
        ..Default::default()
    };

    let result = rpc_client.get_program_accounts_with_config(&PUMP_AMM_PROGRAM_ID, config);


    match result {
        Ok(accounts) => {
            println!("Found {} accounts", accounts.len());
            for (key, account_data) in accounts {
                println!("Account data: {:?}", key);
            }
        }
        Err(e) => {
            println!("Error fetching account: {:?}", e);
        }
    }
}


pub fn get_pools_with_quote_mint(mint: &Pubkey, rpc_url: &str) {
    let rpc_client = get_rpc_client(rpc_url);

    let mint_filter =
        RpcFilterType::Memcmp(Memcmp::new_base58_encoded(75, &mint.to_bytes()));

    let size_filter =
        RpcFilterType::DataSize(211); //

    let account_config = RpcAccountInfoConfig {
        encoding: Some(UiAccountEncoding::Base64),
        ..Default::default()
    };

    let config = RpcProgramAccountsConfig {
        filters: Some(vec![mint_filter, size_filter]),
        account_config,
        ..Default::default()
    };

    let result = rpc_client.get_program_accounts_with_config(&PUMP_AMM_PROGRAM_ID, config);
    match result {
        Ok(accounts) => {
            println!("Found {} accounts", accounts.len());
            for (key, account_data) in accounts {
                println!("Account data: {:?}", key);
            }
        }
        Err(e) => {
            println!("Error fetching account: {:?}", e);
        }
    }
}


pub fn get_pools_with_base_mint_quote_wsol(mint: &Pubkey, rpc_url: &str) {
    let rpc_client = get_rpc_client(rpc_url);

    let mint_filter =
        RpcFilterType::Memcmp(Memcmp::new_base58_encoded(43, &mint.to_bytes()));

    let quote_mint_filter =
        RpcFilterType::Memcmp(Memcmp::new_base58_encoded(75, &WSOL_TOKEN_ACCOUNT.to_bytes()));
    let size_filter =
        RpcFilterType::DataSize(211); //
    let account_config = RpcAccountInfoConfig {
        encoding: Some(UiAccountEncoding::Base64),
        ..Default::default()
    };

    let config = RpcProgramAccountsConfig {
        filters: Some(vec![mint_filter, size_filter, quote_mint_filter]),
        account_config,
        ..Default::default()
    };

    let result = rpc_client.get_program_accounts_with_config(&PUMP_AMM_PROGRAM_ID, config);
    match result {
        Ok(accounts) => {
            println!("Found {} accounts", accounts.len());
            for (key, account_data) in accounts {
                println!("Account data: {:?}", key);
            }
        }
        Err(e) => {
            println!("Error fetching account: {:?}", e);
        }
    }
}

pub fn get_price_with_pool(pool: PumpPool, rpc_url: &str) {
    let rpc_client = get_rpc_client(rpc_url);
    let pool_base_token_account = pool.pool_base_token_account;
    let pool_quote_token_account = pool.pool_quote_token_account;

    // 定义一个通用的函数来获取 token 余额
    fn get_token_amount(client: &RpcClient, account: &Pubkey) -> Result<f64, String> {
        client.get_token_account_balance(account)
            .map(|balance| balance.ui_amount.unwrap_or(0.0))
            .map_err(|e| format!("Error fetching account: {:?}", e))
    }

    // 获取基础和报价 token 的余额
    let base_amount = match get_token_amount(&rpc_client, &pool_base_token_account) {
        Ok(amount) => amount,
        Err(e) => {
            println!("{}", e);
            return;
        }
    };

    let quote_amount = match get_token_amount(&rpc_client, &pool_quote_token_account) {
        Ok(amount) => amount,
        Err(e) => {
            println!("{}", e);
            return;
        }
    };

    // 检查有效性并计算价格
    if base_amount == 0.0 || quote_amount == 0.0 {
        println!("Invalid token amount");
        return;
    }
    let price = quote_amount / base_amount;
}

pub fn get_pool_account_info(rpc_url: &str) {
    let rpc_client = get_rpc_client(rpc_url);
    let pool_id = Pubkey::from_str("4WUGcC5uqJjHxt6pmfyBepPspb9XXyGGviaGvdzDhG6T").unwrap();

    let result = rpc_client.get_account(&pool_id);

    if let Ok(acc) = result {
        let account = spl_token::state::Account::unpack(acc.data());
        if let Ok(account) = account {
            println!("Account data: {:?}", account);

        } else {
            println!("Error unpacking account data");
        }

    } else {
        println!("Error fetching account: {:?}", result);
    }
}

#[derive(Debug, Clone)]
pub struct PoolAccountInfo {
    pub symbol: String,
    pub pool_id: String,
    pub pool_address: String,
    pub base_mint_address: String,
    pub quote_mint_address: String,
}

#[derive(Debug, Clone)]
struct AccountQueryMetadata<'a> {
    address: Pubkey,
    account_type: AccountType,
    pool_id_ref: &'a str,
}


#[derive(Debug, Clone, Copy, PartialEq, Eq)]
enum AccountType {
    Pool,
    Tokens,
}


pub async fn get_pump_pool(rpc_client: Arc<RpcClient>) -> Vec<PumpPoolInfo> {
    let pools = vec![PoolAccountInfo {
        symbol: "GORK-WSOL".to_string(),
        pool_id: "37iWFSqgnTSAfShoBTBzQghwsTtkWAZW3yVzgJWKn6iK".to_string(),
        pool_address: "37iWFSqgnTSAfShoBTBzQghwsTtkWAZW3yVzgJWKn6iK".to_string(),
        base_mint_address: "4WUGcC5uqJjHxt6pmfyBepPspb9XXyGGviaGvdzDhG6T".to_string(),
        quote_mint_address: "HWQToY8QkPQrR8BRJpdsr15iBQ2afbjX87YfuRFgZHpH".to_string(),
    }, PoolAccountInfo {
        symbol: "XBT-WSOL".to_string(),
        pool_id: "99D5oi479AxQpQcfVKkTK6E7r1Y8KJSKhaA9dUBws1vd".to_string(),
        pool_address: "99D5oi479AxQpQcfVKkTK6E7r1Y8KJSKhaA9dUBws1vd".to_string(),
        base_mint_address: "3PSANJRfQiA6CEyP3kguyHQecC5y4rW7TjWEaP5j6RcR".to_string(),
        quote_mint_address: "3QkNwkqmHLfL77m3haUNRtKrkeUa5cx2VU1nRbTSKJX8".to_string(),
    }, PoolAccountInfo {
        symbol: "LAUNCH-WSOL".to_string(),
        pool_id: "HvFhdMkw5QnRK7wVor32UG8fvwJ3hrJy5C6AK2zfAGdq".to_string(),
        pool_address: "HvFhdMkw5QnRK7wVor32UG8fvwJ3hrJy5C6AK2zfAGdq".to_string(),
        base_mint_address: "nPdThNGiecBUFpB6hHb6EFW3VmEEKvi5WF38XxPTC2Q".to_string(),
        quote_mint_address: "C6UtFrUAUWtdZeDqeUqdXbDcNbwKk8WUYv9poGQHPUjz".to_string(),
    }, PoolAccountInfo {
        symbol: "moonpig-WSOL".to_string(),
        pool_id: "F8R9okKt8PvygkZ6bCimxELYb2kUn9E3FDdugfeRUYJw".to_string(),
        pool_address: "F8R9okKt8PvygkZ6bCimxELYb2kUn9E3FDdugfeRUYJw".to_string(),
        base_mint_address: "HEenqZEn3woJrhKurmLusZEmAvY3kCnSzgeGjWWD36UW".to_string(),
        quote_mint_address: "2wMjGCJPbWQa2LyxDbcBtk2svM6iiKagnoQgFaBcT2sg".to_string(),
    }, PoolAccountInfo {
        symbol: "OMEGA-WSOL".to_string(),
        pool_id: "F1kchYKdh9aR7o4tsfvEZ3SdGDLG34ezHnkSzKEvk4gn".to_string(),
        pool_address: "F1kchYKdh9aR7o4tsfvEZ3SdGDLG34ezHnkSzKEvk4gn".to_string(),
        base_mint_address: "5Y4GfERqmtZHVFHNzB6KUSezabA9QqBBS1SWRBANhfJb".to_string(),
        quote_mint_address: "CF6ZTt5dGkzEK9pDUoEuhXFkBxrKVWaMFHS83DKkrh9C".to_string(),
    }];

    let mut pools_res: Vec<PumpPoolInfo> = Vec::new();
    let pools_info = get_multiple_pool_account(&pools, rpc_client);
    if let Ok(pools_info) = pools_info {
        for pool in pools_info {
            // 计算价格
            let base_token_decimal = pool.base_token_decimals;
            let quote_token_decimals = pool.quote_token_decimals;
            let base_amount = pool.base_token_amount as f64 / 10f64.powi(base_token_decimal as i32);
            let quote_amount = pool.quote_token_amount as f64 / 10f64.powi(quote_token_decimals as i32);
            let price = quote_amount / base_amount;
            
            let pool_name = pools.iter()
                .find(|p| p.pool_id == pool.pool_id.to_string())
                .map_or("Unknown".to_string(), |p| p.symbol.clone());

            pools_res.push(PumpPoolInfo {
                current_price: price,
                pool_info: pool,
                pool_name,
            });
        }
    } else {
        error!("Error fetching pool info");
    }

    pools_res
}

fn get_multiple_pool_account(pools: &[PoolAccountInfo], rpc_client: Arc<RpcClient>) -> Result<Vec<PumpPool>, String> {
    let mut accounts_to_query_meta: Vec<AccountQueryMetadata> = Vec::new();
    for pool in pools {
        let pool_id = Pubkey::from_str(&pool.pool_id).unwrap();
        accounts_to_query_meta.push(AccountQueryMetadata {
            address: pool_id,
            account_type: AccountType::Pool,
            pool_id_ref: &pool.pool_id,
        });

        let account_type = AccountType::Tokens;
        accounts_to_query_meta.push(AccountQueryMetadata {
            address: Pubkey::from_str(&pool.base_mint_address).unwrap(),
            account_type,
            pool_id_ref: &pool.pool_id,
        });
        accounts_to_query_meta.push(AccountQueryMetadata {
            address: Pubkey::from_str(&pool.quote_mint_address).unwrap(),
            account_type,
            pool_id_ref: &pool.pool_id,
        });
    }

    let accounts: Vec<Pubkey> = accounts_to_query_meta
        .iter()
        .map(|meta| meta.address)
        .collect();

    if accounts_to_query_meta.len() != accounts.len() {
        return Err("Mismatched account lengths".to_string());
    }

    let result = rpc_client.get_multiple_accounts(&accounts);

    let mut pools_info: Vec<PumpPool> = Vec::new();

    if let Ok(accounts) = result {
        for (i, account) in accounts.iter().enumerate() {
            if let Some(account) = account {
                let account_data = account.data.clone();
                let account_type = &accounts_to_query_meta[i].account_type;
                let pool_id_ref = accounts_to_query_meta[i].pool_id_ref;

                match account_type {
                    AccountType::Pool => {
                        let mut pool_info: PumpPool = PumpPool::deserialize(&mut &account_data[8..]).unwrap();
                        let pool_id = Pubkey::from_str(pool_id_ref).unwrap();
                        pool_info.pool_id = pool_id;
                        pools_info.push(pool_info);
                    }
                    AccountType::Tokens => {
                        let acc = spl_token::state::Account::unpack(account.data());
                        if let Ok(acc) = acc {
                            let mut pool = pools_info.pop().unwrap();
                            if pool.base_mint.to_string() == acc.mint.to_string() {
                                pool.base_token_amount = acc.amount;
                                pool.base_token_decimals = 6;
                                pools_info.push(pool);
                            } else if pool.quote_mint.to_string() == acc.mint.to_string() {
                                pool.quote_token_amount = acc.amount;
                                pool.quote_token_decimals = 9;
                                pools_info.push(pool);
                            } else {
                                error!("Unknown token account");
                            }

                        } else {
                            error!("Error unpacking account data");
                        }

                    }
                }
            } else {
                error!("Error fetching account data");
            }
        }
    } else {
        error!("Error fetching multiple accounts");
    }

    Ok(pools_info)
}
