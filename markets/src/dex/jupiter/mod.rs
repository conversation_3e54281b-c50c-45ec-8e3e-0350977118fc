use crate::dex::jupiter::model::{DerivedPrice, PriceResponse, PrioritizationFeeLamports,
                                 PriorityLevelWithMaxLamports, QuoteJupResponse, QuotePlanResponse,
                                 QuoteResponse, QuoteSwapRequest, SwapResponse};
use serde_json::{json};
use std::collections::HashMap;
use tracing::{debug, error};

pub mod model;


// Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB
// SonicxvLud67EceaEzCLRnMTBqzYUUYNr93DBkBdDES

/// Get simple price for a given input mint, output mint, and amount
pub async fn price(input_mint: String, output_mint: String) -> Result<HashMap<String, DerivedPrice>, String> {
    let url = format!(
        "{base_url}/price/v2?ids={input_mint}&vsToken={output_mint}",
        base_url = "https://lite-api.jup.ag",
    );
    let response = reqwest::get(url).await.map_err(|e| {;
        error!("JUP Price Error: {}", e);
        format!("Price Error: {}", e)
    })?;

    if response.status() != 200 {
        error!("Error: {}", response.status());
        return Err(format!("Error: {}", response.status()));
    }

    let text = response.text().await.unwrap();
    let response = serde_json::from_str::<PriceResponse>(&text).unwrap();
    let price = response.data;
    Ok(price)
}



pub async fn quote(
    input_mint: String,
    output_mint: String,
    amount: u64,
) -> Result<QuoteResponse, String> {
    let slippage_bps = 40;
    let swap_mode = "ExactIn";
    let only_direct_routes = false;
    let url = format!(
        "{base_url}/swap/v1/quote?inputMint={input_mint}&outputMint={output_mint}&amount={amount}\
            &slippageBps={slippage_bps}&swapMode={swap_mode}&onlyDirectRoutes={only_direct_routes}",
        // base_url = "https://lite-api.jup.ag",
        base_url = "https://api.jup.ag",
        // base_url = "https://public.jupiterapi.com",
    );
    debug!("Quote URL: {}", url);
    let response = reqwest::get(url).await.map_err(|e| {
        error!("Error: {}", e);
        format!("Quote Error: {}", e)
    })?;

    if response.status() != 200 {
        error!("Error: {}", response.status());
        return Err(format!("Error: {}", response.status()));
    }

    let text = response.text().await.unwrap();
    if text.contains("routePlan") {
        let res = serde_json::from_str::<QuotePlanResponse>(&text).unwrap();
        Ok(QuoteResponse::Plan(res))
    } else {
        let res = serde_json::from_str::<QuoteJupResponse>(&text).unwrap();
        Ok(QuoteResponse::Jupiter(res))
    }
}


pub async fn swap(user_public_key: String, quote_response: QuotePlanResponse) -> Result<SwapResponse, String> {
    let url = "https://lite-api.jup.ag/swap/v1/swap";
    let max_lamports = PriorityLevelWithMaxLamports {
        max_lamports: 1000000,
        priority_level: "veryHigh".to_string(),
    };
    let prioritization_fee_lamports = PrioritizationFeeLamports {
        priority_level_with_max_lamports: max_lamports,
    };
    let param = QuoteSwapRequest {
        quote_response,
        user_public_key,
        dynamic_compute_unit_limit: true,
        dynamic_slippage: false,
        prioritization_fee_lamports,
    };
    let body = json!(param);
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .header("Content-Type", "application/json")
        .json(&body)
        .send()
        .await;


    let res = response.unwrap();
    if res.status() != 200 {
        error!("Error: {}", res.status());
    }

    let text = res.text().await.unwrap();
    let tx: SwapResponse = serde_json::from_str(&text).unwrap();
    Ok(tx)
}


#[cfg(test)]
mod test {
    use crate::dex::jupiter::model::QuoteResponse;
    use crate::dex::jupiter::quote;
    use crate::dex::jupiter::swap;

    #[tokio::test]
    async fn test_jup_swap_should_work() {
        let sol = "So11111111111111111111111111111111111111112";
        let wif = "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr";
        let amount = 1 * 100_000_000_000;
        let quote_res = quote(sol.to_string(), wif.to_string(), amount).await;
        match quote_res {
            Ok(quote_response) => {
                match quote_response {
                    QuoteResponse::Plan(plan) => {
                        println!("Quote Plan: {:?}", plan);
                        let user_public_key = "B4M9RqyTF9jF5Zb8ZG9ZeLkvWXCeKMAq6TdE9L4NqUr2".to_string();
                        let tx = swap(user_public_key, plan).await;
                        match tx {
                            Ok(tx) => {
                                println!("Swap Transaction: {:?}", tx);

                            }
                            Err(e) => {
                                println!("Swap Error: {}", e);
                            }
                        }
                    }
                    _ => {}
                }
            }
            Err(e) => {
                println!("Error: {}", e);
            }
        }
    }
}
