use std::str::FromStr;
use rust_decimal::{Decimal, MathematicalOps};
use rust_decimal_macros::dec;
use solana_sdk::pubkey;
use solana_sdk::pubkey::Pubkey;
use tracing::warn;
use crate::solana::dex_integrators::raydium::model::PoolState;

pub const POOLS: [Pubkey; 600] = [
    pubkey!("3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv"),
    pubkey!("DEGuJP9EuGN9U9sXmPzFaB5d5C1ZBqzFjnExjGdE2pt8"),
    pubkey!("7sLRKKr72a94GUz1LQKgNd11ZU4kqCZEYqQZNtuMm98E"),
    pubkey!("J9Vji2wFLSXxxPg7FVU3u5VfY2Cey8UGY4nqi6shxu6V"),
    pubkey!("96pbgTSWNyJtZtF7phHJGKiGNoYF1aFNXCbgUcbGzStu"),
    pubkey!("HtafMt3H3Ao86ohbxEMhyweTZxvRcoydjYAV8r5oz3i7"),
    pubkey!("2AXXcN6oN9bBT5owwmTH53C7QHUXvhLeu718Kqt8rvY2"),
    pubkey!("8sLbNZoA1cfnvMJLPfp98ZLAnFSYCFApfJKMbiXNLwxj"),
    pubkey!("2zVV22uNWdJNmkXpj5vCrMzwHGBoJdsyV7qACh29sK1w"),
    pubkey!("LpT37CuaFCa1qUouwfggFQuTZMGTHANY3YSqLm3Q3ec"),
    pubkey!("8hRqBN3mwhipvL9qq6dSXbo3R1stSHuB36Vudwc8L6Wt"),
    pubkey!("3dxFvyohty6KRM29bgdFNfSFCmTxEVf8jaGdiDCCaHuQ"),
    pubkey!("GopsSduLcPWxY5srsAmtU63sb96WCttqAAX7T7ddKFdE"),
    pubkey!("A4Rz8TvgMN2a6bLFvQQmyQiPH1CtVdMF92CKqERiJNN9"),
    pubkey!("AwaVyGAF3N6K4YRUJk55Ui1GquKhKdD6vTCmVvCzXpKf"),
    pubkey!("3nMFwZXwY1s1M5s8vYAHqd4wGs4iSxXE4LRoUMMYqEgF"),
    pubkey!("8EzbUfvcRT1Q6RL462ekGkgqbxsPmwC5FMLQZhSPMjJ3"),
    pubkey!("Bb5WMkUUig126hyZG1HdPGgBo61sdRHtnfmkf23fZuqx"),
    pubkey!("5zkxGGcX762XKbHGyA36jf4fnM6aYxbqLN1tzrur6wC2"),
    pubkey!("3NYuigEAWGkksZLk8oRvZWjyhexcNiM4LaeUzv1qQ78L"),
    pubkey!("BnCw7LBwqa32HUSKsxjNXDYFjnMkKqqxTijs8o7wkwGg"),
    pubkey!("56dATm1jCLKUqt7P6xpZRjvjd7zHbR8RGPdZ8vRdqEKg"),
    pubkey!("BgandSQADV5DQ8pQWy36GB13r8xNoVFkbGCYH4JUeo6K"),
    pubkey!("7JokTxaq2JmiQjUnF4MRnjrbvv9Xdz1qM8o4gsG2fdwS"),
    pubkey!("H9rG6ZtfkRE95tEZpZ7ZYE6SXJ5bk2EL2ttLQoTHzDXr"),
    pubkey!("CYbD9RaToYMtWKA7QZyoLahnHdWq553Vm62Lh6qWtuxq"),
    pubkey!("EX3QhnpeHcem4rHPW7YLSfqmmRRccNwoaT87Lu3LvY7Q"),
    pubkey!("7S9KVtU3XBRvkCytbcsybQyMXgL5CKfhW8Ds9PLJaRZQ"),
    pubkey!("71B8RSEyaXW89Cs9xypZcAxzPHg2Fw1qceBCicrgqA9J"),
    pubkey!("CDqSnM5phd2UbMbinYMEtCBvQDJkyhfBfDsickbjv7jU"),
    pubkey!("ELFoCNqS16vrgXnfCwJ6RJhodcVhn8LEKhjP9dqXXNUH"),
    pubkey!("GEacZ94pW7egZsPLgNWXrWBnA5qPTfbRUU5epdfjaLF2"),
    pubkey!("8ubPrgPWU9YqB9wyJsLXhEqE9wQJnFt4Z1cX4mSAaxsn"),
    pubkey!("2s7VniAqnQFQCp2hSh8PDKreEZWE31kfJKt7LjuFWQg8"),
    pubkey!("3sZCWJnQXHBcv26uuSWxtN9VyCRNqpLAP6iNzVmngfB5"),
    pubkey!("8VYNir2yasvFvPmhd7d9JamgCjKVrRSgAVv68MvbYmp2"),
    pubkey!("7ayut1ewhuSsnGjaMn544YfNL4Nm7kLzEsJZqgvdLNP5"),
    pubkey!("5ajQtyVkFqWMPvkSJpDsTjrDKUZYBqaFZx4Bnqy4ghxU"),
    pubkey!("EMLahfHyhJT4f7HgBc4zRUtZpi45GT6MG1rLDvi1hNrY"),
    pubkey!("eFGhJbVxexHC993Unfu5nf24XsWQJUaMfJiQeTWkSEU"),
    pubkey!("DbnLRaNXR1F1rUs7v1jAbdtrdZTxGvFs74kxxpkJR2TX"),
    pubkey!("3RNhGMKa8BGsLY5biYpAdCyfwdATnSdihvB42kg7GHZd"),
    pubkey!("EAjiM2itd9yYfVyRMycDQY4nDAvPbc4GmfghUqQRMLSA"),
    pubkey!("BZtgQEyS6eXUXicYPHecYQ7PybqodXQMvkjUbP4R8mUU"),
    pubkey!("9b3msAYae1agvVnNCb8Nf8FsU7SUXvuPvwtktabWJbK1"),
    pubkey!("86dtR3PAi68xveDarpDxiSuk96TVkxiUS92Xf2NoqMAm"),
    pubkey!("8y19LEvif2ZTUoQw5p4RbWy7FoFagh6qbqSgvPzMhEFA"),
    pubkey!("2ndLJpaz9nppjVUkQGMCcAdjLPqJerHJ7fWrtCrqyuBf"),
    pubkey!("GxxwxRHwR5AtS72DihcLN3H6zdnCYhnrKd4tmEYpiY45"),
    pubkey!("8H7PQCCKXstoATXfKL4BWwBMzEM1x6CUxuBvrXC3knZ8"),
    pubkey!("DxHDmgMeeoNxGQsSYFQphNaRBky1wQZ4z7LyKFRacfbH"),
    pubkey!("8Utrd6xBcZBNWFD5B2korW53b4cxHW8UG3iT9fa3S7aB"),
    pubkey!("CHTxykMkN54fjpCtj9jXguZa3bt9ps3wFeBSdmJy9aw5"),
    pubkey!("EJZChMmCt7ecL2GC5DZV6VBAPD8xJyTRKWGWNtXvRTTW"),
    pubkey!("4ZyxPiYFm9Tob7tYVEo3JzVhe6hfGJKCbNgahGE9qJ8M"),
    pubkey!("C5RKW43xL66PXEBdrGTorfGqjKjMp7NRsqrQUXnXzxhA"),
    pubkey!("D7x7vkrmkQARb4ke5Hxr84wa3Kj5wGsmz8KjHbeNdoY3"),
    pubkey!("FZNaNHXZzAx7NpEY8YxrGHd8nxMBDdxF5tkQjKu5Pfek"),
    pubkey!("BdAnR9bjTwGRxJNyQtc7UETwHgXG5LmDFm4xy8zeYhmo"),
    pubkey!("8yR49cfaTsV7hFvZbm3Q1azYeDb4cF6kCybt9itmUybB"),
    pubkey!("G6drsaPCR3pxsEmSTAc81kW1EL3kFAFwtSAkzUZXmgH3"),
    pubkey!("Ef9i5YpZuJfxNbkDdkkKqxsMbKKyjcsdCwBuVC2Kfad"),
    pubkey!("5iLYfrJjHiVodafE4yvw9u6pBxtLWfuCL3GrNf8tSbLo"),
    pubkey!("99dqf8mVNFwv1FE4m46cqHtwEprNkVYBWFVTFRNYbkVG"),
    pubkey!("DT9f9eUmCvJrMRX3qtr78chacB4TkR36Tn6X8gxx6Pfo"),
    pubkey!("ANANFAhY7QGzZygLWvsX4iRfC9HnbEhdAw17kmNGzB2H"),
    pubkey!("9FYeWmGdAcqhd5VXVZJakGPHGUEMicUvUtYemiq6sJQE"),
    pubkey!("GJUpPTaeogcqGNDeSqKysyp95waE5YpU8hduSC3TCWLY"),
    pubkey!("756ghRPWm8ujtrnBC2cmKKpjRi5MvtpSZ34ZFvX2XHFY"),
    pubkey!("FNbj5k8tWwr24HoRfGHtAVaH68yzLH7LVPnLUs8zEWWE"),
    pubkey!("7HBU56YMMvm6SV97t7PXhiiSmj67ownYBjKzJUr1j4Zq"),
    pubkey!("BBNBxUHmgHNP2g96zfEt3NpLeHJMTH4LiqEG58dXZ5CL"),
    pubkey!("Gaj67q6c5E2wTCfREJhB8MQ62tN3pP8gRRrfMn1Zw8gN"),
    pubkey!("HgJjVYakLSYWBpYg1wZCL8j6VAihY8qphkB4rBxeNAK"),
    pubkey!("7jDEdU8RbwvqqnNCbvLKw9dBrNfiLjS2XTPR1rwesVa8"),
    pubkey!("E64f2eZXhak6yoB6cqfLfvEs67piczfJ6Rq9pvpc5UjA"),
    pubkey!("Dk5hADGJfhnQrFq5JAck2M5a8EqKqGGxqzbRKwr4UWBr"),
    pubkey!("pBepKfFNUU7v3onEbUpRhjUZR7ajogXCAPiU9fBWTVK"),
    pubkey!("2uoKbPEidR7KAMYtY4x7xdkHXWqYib5k4CutJauSL3Mc"),
    pubkey!("D4cRtPonRgaMPKE4KD9SJmANtWuvXuumXGBdGV4kytLk"),
    pubkey!("7xFiZTShVb8HGFa8GSLqqMiwg1ZeUpmeA8mgCLdXY5DG"),
    pubkey!("7wGtVL5mVdCpi9e9ttF4J981ZbWCbXMPwPKRsPen8iWe"),
    pubkey!("2gvUHU8uv9b6WpCsxxAMwuFkEqohmEeiQ2mBdWgNpqJn"),
    pubkey!("Ci2iH61gBGSMDcWiwizBe5zTq5Szey4m1sYyagPy1EjY"),
    pubkey!("7WyZCV4bSjhB13VMTqNQXETRA63y1C7wTHsALCpGYpA3"),
    pubkey!("yVNqRWvNxaB1JBmcE8j3ziowWbozjP1ceYUf28zJTfM"),
    pubkey!("DmKE11QRtaxi1Nz1UL7oGpkWpxTLfY1U3VmoAJaN7f68"),
    pubkey!("3o5U1zBrZ81MH2nb9qtD62DHknZ9NN3YD1Auj4Qi95Jj"),
    pubkey!("AKPdnKRAYDFWmCbHKC3hB6yakMFq4HiW9NpPEGU4Nwyu"),
    pubkey!("DtUdppNk5iwmNaVW4hLorLbno7GEudrwg8Q5uXQYQR43"),
    pubkey!("GU14YF2Ld1RAW6gz9q9L6ZSx818Nri3rRbJDqUoDb9GZ"),
    pubkey!("BBZK82Fp2jRp69VDEHvakVsvdFLxCZEGWPUbRbEfhx2t"),
    pubkey!("H2ovYrfiPHQvvan8bmf8mRzBwSRchXwVqa7iP7ZJQk3Z"),
    pubkey!("BEFEjQVdK3Bk4vGmYg8iZ7w4Ae3tQhf3cKBomKyJiL6m"),
    pubkey!("AynAhjMJfuUZw5yVokRTo5hYTZ6y4xWYqugdChbFSUMS"),
    pubkey!("Cbr6pNdb3yxE9C9DYqUQuQHwYhEVDC3oGgRchPdWUDuU"),
    pubkey!("AS5MV3ear4NZPMWXbCsEz3AdbCaXEnq4ChdaWsvLgkcM"),
    pubkey!("89eNu6xFsgW24mxWRaiAC6sgsB6ZuKVk7eVpesmtvcJh"),
    pubkey!("8sN9549P3Zn6xpQRqpApN57xzkCh6sJxLwuEjcG2W4Ji"),
    pubkey!("5gNjgkHtLGH16bmC6Apyc2cANjss1YKABPsCZraaciZi"),
    pubkey!("D3oConQPSKcvmEtat68ypgfee3fttFFEyQzUv9rdYzf5"),
    pubkey!("4XYDm9SBuyYcUVQ6NwzTxt1efPHXnP2cB1XJGj7BJVWq"),
    pubkey!("BWxBF2cu7eYUk1m3ecvCoggfws6KvGekSzpPc5hekRU8"),
    pubkey!("CJGmWv38C3ckAJbpTJSkSa8zPF3ZBpYGHxSB4r7QEtcu"),
    pubkey!("CLBhxfsxLRJUas444hCFHx96H2Ei41tM9zSwqcGwHLiV"),
    pubkey!("7sb1qGMyvstwX6PFfeUxxr6Pspq46re7uXtBpvTkuK7V"),
    pubkey!("6LmZVZutG3MVAUpnbLw5g1VPaBLEoVJFACB5xp2eUzm9"),
    pubkey!("2NRsuLWgPfTGcv91HsVKDv3vmCean7cTB4wRToRz23GN"),
    pubkey!("UqYKdsVCTjv7gRtkhtYAu7RYQXVxFKadFrhoQ5iPyHu"),
    pubkey!("GWcRx1ziYWHFKXYqsQpf8TeARyCYgQCPkQsyMes5L5r1"),
    pubkey!("Fj9ptU389Znsrx4kGRsmsSLLdGnN1kGNxHgN27q88b6n"),
    pubkey!("FrSuq29B3JmvXazvDL1niNAPEWVH967D6D1Yb4vVP2wh"),
    pubkey!("5eBYfX8UavWAoWttdvArcgC2en5tFYcuuaFoyRnCN3Hy"),
    pubkey!("DThmSV9cFJTFxxj5uZVeLPJYXTpbrAKPykEnLQPTe31n"),
    pubkey!("7jhf1iMrKKbNoxjGXVM9TsYnLnbTivBE9oduuVWg1Xr3"),
    pubkey!("6C6AuJrwHe8Bc43x73Ki8967fa6X68GkbvxAJ9eRLciW"),
    pubkey!("7Lc9pb79FQcBTmqVkkqBLXfMX1sDKJUnYWs9mLsCGNrz"),
    pubkey!("GoRWdisEzAfhQvGHwBDuvPEUNjY5hoGtwPcDF2NWdRSE"),
    pubkey!("8qXyFjQA7G3y21cLZQX2SfsjjZ5hJrUiE2m5w57CKvgp"),
    pubkey!("ZPMhh2JAMQ8AhXjyKJEd3tAqZQ9GHsXWjeorb2Vtfsw"),
    pubkey!("Hq49cq6Ddy5qT1Wn5TsdsQ2qTsJEDzdBsJcy1eC5jEQh"),
    pubkey!("HV9cCQaojva7QKTFVEhih9BPs1pnN8MYWkEdes3ANSJB"),
    pubkey!("G1roeUTsGeAoNWQT3kKaBJSqwAphYcLLeY51eSm2dmGM"),
    pubkey!("FWoVDWUehcYwtK6tz5NRbnFsDUQurMzj1xATq9R5ZsTq"),
    pubkey!("AiwssXrFqbgG3L1tpJxuJayCptjjCUAVikwov6Qfs5YS"),
    pubkey!("EYsFeBJFR3UtSRTYGREi8W6gD6EVwhAvP7PaC4MrHEYe"),
    pubkey!("7vVbkdnXBCKC6eU4oQhyqaYcDwfXJmNoDnBjaHBQnHpJ"),
    pubkey!("DwxS22q9nMJzasNNu4mud6uQBbnBZdU9ChD64TFvNZCZ"),
    pubkey!("AqxWWRwiUR1ADGcZUxZf8Gdnfhh4z5zbPrBBESPMw6bN"),
    pubkey!("BGPE9x2ddqz8CdcR5H7SE8cRCu66JBkySe4zxytU7W9j"),
    pubkey!("FZ8MJvdTPbp8juhtFCCzb7LsKunhexgzShGQ59SUJkNL"),
    pubkey!("EZqEqkEUwK8nWzUB5A54K7BThLxkZXj987HJdiVWK8DX"),
    pubkey!("37nVgDa9qPJLPqxdeALYT6D7FHPr5didzi9E5CaW6tPr"),
    pubkey!("2XwxYxnJ8EnF1TwqZiAoPHKCxXjSc2i2Rnf7GU98z8D6"),
    pubkey!("6DGbvBisehgQYmKW68UNLUybanvSrXPWurmUTdzJ9kCg"),
    pubkey!("D6R5yQaXCNeqH38z6NXmw85sTJ94JCSTB94TJ9w2JXik"),
    pubkey!("7C1KQvCiY2rKHwXZoLZX9QBa3JtQqSgfwaicWaxQkXv4"),
    pubkey!("8k75v9FSArwoFbKz8Xr7aW6phcqU5bDWLBbN9TeGzQo4"),
    pubkey!("9VWW58zCq5vjUnfZEPaCa1X2ZWUeFQ7aQDFdTmvRWzWB"),
    pubkey!("8mCHHf9XzAG1GGW6Fm6TZWtiabcoufb5E9PmjdU8raEY"),
    pubkey!("8GuXrPq6deS1ZzNXYmBjVZ5P48cAmKGrhhpdAn2FURPC"),
    pubkey!("D3UkYYkR1AW94TZK2qt4oSE2E3S4yWKrZhpLkasPnXGi"),
    pubkey!("BFv2LHQxfbcsG2BgXLiJ2VdYAMzeqkN1xxzHdExHaVLE"),
    pubkey!("9Wotm8dQPg6Cc3U5Zum4y65UnZETa9W5gZKMeGMGSJVf"),
    pubkey!("Mvqtnj3vggZLr7UsKtcTsKaGoNosePog76dbKCpes6m"),
    pubkey!("93tujogYT8gxTdiWrnQ7AXGskCA9fd7b8tGAD6wjFABp"),
    pubkey!("6k3h3rfc9p8xSup3H8Uz2pqZHjJaoUKA2NmemunPjH6X"),
    pubkey!("EtZMUARsg3VUrSFn25mwjd1pRcPh72vMpnxANMYr115v"),
    pubkey!("BazGPmRfSPnP86HLkbc3CRcGL96iPFJUkEFUSS7gSSUz"),
    pubkey!("H6Bx3Yq4cavuVpNMhHpy96bzvZbbCXiR9P2nVqmAuFxQ"),
    pubkey!("Heu876Ai4SPaytEBFVwpzVniBhATZcTDn77Hefg3m33N"),
    pubkey!("DcctpsPm1VmscXZ7sDUU3wi4cQMU9JhsJdmVXBr7TcxE"),
    pubkey!("Cdw1sywRDUQj3Q9EvbRN7sHe3nHcT4n9LKvsJLPVp8Aq"),
    pubkey!("J9Y5UonVM7WF7NMX5iRk7wLVHGMks6gxbeaKiU8ScCG9"),
    pubkey!("6gRPbmLY7rPPN1q8Hej5HcYzScDr12pcyBWEezCZRuJE"),
    pubkey!("F8T12m9XTan3zUg2KVpJ2cNia2iRNrK6hcTshUc6HafE"),
    pubkey!("HUtoAV49bBSNxxXTMP8DNYj9hPevk2mqZLqGAFMr9WXY"),
    pubkey!("5trmVNXuKSFd7JybszkWHMfaftV3UsxWW8eK7uArBkCN"),
    pubkey!("Crs3rSJbskzobVAGahsNSTupsenhcDzMqBAkzSrHLMr5"),
    pubkey!("8ZaVBedBinEBhiVbL9Y1dp8wpCAdwyzAMfcdmjvXMLH1"),
    pubkey!("CySogafx48bVnvhq88KmrDGuBZwVKQDnsFufsPi14bE1"),
    pubkey!("FxviLEX1wbGuMemtaRye2kWCP9Akho1g4noZC5vD4GtS"),
    pubkey!("2fE4UQ4JBRMsuUpacV4GqG9S8C3hrVmM6XKtuR2TQkQf"),
    pubkey!("ELRw5chFp6PPyB6YukgtRV4GuPiK3WivDN7MtEwEfjnE"),
    pubkey!("2rp1wQhYbrNegwrFbRYLL84TXZZoNvEgkobr3keQWirH"),
    pubkey!("CqebPehGwCTeTzv6246Tc7sDCWiHAVmHNYhLv82cKC3S"),
    pubkey!("2p7Z5piTRBEcwpipXKdwKyhgdjxAHNKMUsbJbKzqwyC7"),
    pubkey!("F4vx4TJ1HWZfCWCgzgXKhbSG86jqbt5BUUuqaUyurKz8"),
    pubkey!("67R28faFpTSqFcrmEk4YeYU8B359ghMi43Mf4ECziFQn"),
    pubkey!("5Q3TckTqoAusoDLqyTyQoVtDYuQzeEKjoaiqw1ErPQDJ"),
    pubkey!("Fq5zvjwZs5XmHoVCBmabKXNJE4tC43fHVMVX4swLaPCp"),
    pubkey!("C7wWYvezv3jvMfgyMTE7m1YZBPhRXMTnZsvEVXceGKQy"),
    pubkey!("8B3aAkep67JAah5i82DcKuWVAYVjUJpHoEJCaYJLFqbh"),
    pubkey!("EpG1FHi5aBycg2RWy8j4T7gir1UzDDC3B1ZS9jojvEnM"),
    pubkey!("D1ZAfgbB6Xkhhbe2eVa59A74XqVm6kkfvDHLz8X4yau4"),
    pubkey!("HGWtr5Vffj38mQrf4DhCqCDPZZfEa2ktJpwfeE2YQwDR"),
    pubkey!("ERoMbZnewKTroBUD6ysyKT4khDEa49DjRNevGUFuSBTT"),
    pubkey!("BaCtgEvNr57epMgEb4KEJjS3P3gDCAM8SDe4LJvCfg8F"),
    pubkey!("2PnMTRicuJExwYcV8J4ALri2BXsNR7EK87mgz3ztriSX"),
    pubkey!("HmX1wdSZAGBxR3EEPcXUsY87b7tmc1tVcfHWodyDF3FR"),
    pubkey!("********************************************"),
    pubkey!("********************************************"),
    pubkey!("CioFpzgnSizBMXd66kgx3owXa9X5AxAupoCGPRKhTpX9"),
    pubkey!("J2g5eH38fnyFRfLcqT7JYpko7X3fE9rrDoHgRQrTcAni"),
    pubkey!("8LfjYADySw2RbmsfhomJkYqmDrAJfRVt99TiHjQMFtoU"),
    pubkey!("Ha9HrfwZinxcio7GshPg7xcdvqkr8x6kB4Tpevm9jb93"),
    pubkey!("CxvD5bo8pqwqR2noqQJtXpD8xemLWfuDdQxeyJAZsNJJ"),
    pubkey!("YaNxZdqQovgw1kBrvPTanu9a3JrzoW6tTqkYGipBZhD"),
    pubkey!("********************************************"),
    pubkey!("ENEsWycuyPHP31drTC5PY6h3STGvizWUuG9WYgyqsYrg"),
    pubkey!("AsFQPJG6ctoGQj3Gdq6iWGm2D65u5n2Cv9xT2MS8TNoL"),
    pubkey!("ExC7aUNZh5YnZTfMZRbb33bnFydHEF89cZbsqm1NcU6e"),
    pubkey!("7NkqD8Dk1WbKckJ2NfS7drXmHgrEcsDdjJUqP9KNnv68"),
    pubkey!("B4Vwozy1FGtp8SELXSXydWSzavPUGnJ77DURV2k4MhUV"),
    pubkey!("xLVgW4G2vrz2Zu8zzvEhcJg1UudbNrifWShw32RmrzW"),
    pubkey!("GQZ5xdooBdBMP3AzA9wk9P3gforBcW6PSSZBE4QRP7xs"),
    pubkey!("FrXTop6wRiMkAc4RvVWGUgPB7Tt8Khx5owvs1NGryhxf"),
    pubkey!("2Czy5LCzBJTnfdShMpoYYByZrjBxcp3CW3iJU8MG1ffg"),
    pubkey!("2BkEu2kqKDVjFLi5LBQ35K6G9E5NqE5oa1sH8UHMRa9C"),
    pubkey!("EA289L79eELhgzTD2d8NJyu7oqRGqcp14kXmFZYFRkmr"),
    pubkey!("6AVV2uV1DzX1Y2oSXamipar3w945hfJzrEbQVHtZqdcT"),
    pubkey!("CYmzZSLYRQ2kDQUAmbegvFmEU2gxtKgpLhiGcMaqDfEf"),
    pubkey!("F8cjWidRapx4tv6X2fhMpZK1gMBJFt85CPJ64fFW3hGe"),
    pubkey!("DM71rpEPffBtvuaMpGRw4AZjLD8bmHdCDyuQtdtJhq5i"),
    pubkey!("EzxddUBjjwrLRRjPVhDAvDLHt6AM8sS7cpmGwFHDtccv"),
    pubkey!("jk3gcZbq8jAv7D3C4DpWQwcADgh7ZL2s11UY44Z2T9m"),
    pubkey!("9NV5TEBSaWUVoPRiHd3XpEmu8KWBnh6NvFu7HNSUvRUi"),
    pubkey!("8KbrpeSRYXYjWSSdG7gE1tR7Go8MmKKxKaei1gGc4U7Q"),
    pubkey!("HHJisjYXphdTq4WMVXgzrP2m1LW7pa6W9rooeGiGX4CZ"),
    pubkey!("FCAjNjmqJ8UAGPjeHEKTvBUMYC7kK8EhgYVEVsL5dkKm"),
    pubkey!("GTJ2S27UL7yZ3TdTwpKjfNcxeEZRkRPHjpj5Fubwb8Mk"),
    pubkey!("BWqpMYWGfiRw4MRHW6CSiMUTeMsdiFUQ8braUHgtqX66"),
    pubkey!("29JfNtCGAgNE7LycPkc3gBWi64trMfy7hWs4eNUFjoTu"),
    pubkey!("e5ugqrhQqeCrnmXWsidpRZ95twwRGxZVsk5vUDDGA8E"),
    pubkey!("4jP9PA8Xj5GdVw7XjUceQmGz9K1yz3459MiikqNX3vFy"),
    pubkey!("F5oK5zdyUktrzTgSJdBFL73xUdCLcHfHfHdBcpydhXoK"),
    pubkey!("qtNTMEEXBUBroUxhhEpfVCE3ZQ93t5vSk1NqP9xqWFa"),
    pubkey!("5qe6vZqKA5NGpQJD5upu8FN2ps1x1knfTLZR2WBLH1Fz"),
    pubkey!("3i2zZTqZQ4G3Rh9dKz22P3x54ywz2eNKwnEnVrGxywqY"),
    pubkey!("FVbWnGAHv2H8EJA4A9EQZ6j36nXorTyrsfKxUbmu7pRg"),
    pubkey!("DDeMrFsVeFJnZiip4e1pMVS1xKHf7zxPSUo39yUAvcEE"),
    pubkey!("7MP6FQYJDmva6BokF2ngxDzL5edHXX7hbUesEi21btTM"),
    pubkey!("3JiXMY7eQFbDESJeLd4WcCx2eN2kPhAxEb8xStk8Pbcf"),
    pubkey!("CAfCHAvg6NJJ2goeMk5BHrBwEcLL5hmKdHTeeRday9tn"),
    pubkey!("6LCS2ieDeBWNEJHu5cJVHtQWxZziy64LJ3rdAcGedcch"),
    pubkey!("KJjHWVAQszC3qLHr1Szrj3ePmPGS7uH4HNHmeRMsHbS"),
    pubkey!("H9whH76F9Tk8MJoAcVsifASetcrJdcHakRbFMNvzRNeq"),
    pubkey!("HvbnntP34NJY3KgDiDqvhPqqKt9UMmKdi4KEMo6JzUxu"),
    pubkey!("DPEhQvgV1Rj8bfu96MUemCB166Huptf7KeMqtdYo5L2v"),
    pubkey!("CHPdWe2aAomBxHQE7yYn4u8GTdUdVBG6HUsH5GUtogGF"),
    pubkey!("5wzBKhCsD6ButZjgGf6d6rVDmg4bVCLkQYjtiKeor516"),
    pubkey!("6GU4vHxm5XynkdmhyKw5RqyEpNXmBtNbH59tpXDLGDE8"),
    pubkey!("3z1CUyAhxosF4RLx18HRoWXU5Wced89xyBfnPMgGVVuo"),
    pubkey!("HsDZSwkbHjM8Td6bahxCL76e86gs8kEz6Ay9Bd6K8AnG"),
    pubkey!("CkTbz5pAJfyjvfzmQcPf92mc9ekmcMRWxqyvXzT5GG9X"),
    pubkey!("HCSZdBQbsnyDuhhxQHgKA4CvZtGGDuqoa3YME5wzJ5WH"),
    pubkey!("59vWGaNtHbzFAuQN9qmE8QMhwPoMu1rtVLgLPQAJTjSu"),
    pubkey!("BZMN8dDBJ6vabqJvtRLFXh11UtXfhy9ciyv5Hpu9V5iB"),
    pubkey!("DGnGXpZqkSd8BhBoCsNNcKGAKZJrnyvnCT5vXb3PK2UB"),
    pubkey!("EEkMbgYJzgYNUJsnURtL3jkDdkAws5RbDL7ukCUFNRMV"),
    pubkey!("5hCP59BhGgFdiB2BdrMzP1nv3x2PSExcNTSbnWAzfAb5"),
    pubkey!("FWoNSSYFeUeaV924VB2PLPkDJZj9d6ob3DjWym5YFu8g"),
    pubkey!("6nEZnqEv8iChBH64rv623s1TbJh9zAYc14tKSsMyAoVd"),
    pubkey!("FL8XmrG6xBS277bYq3G42xDEstwgStLjmbKrzSyrDWsN"),
    pubkey!("4a47PFVP8Q1WELCSzSPnBMtGJueLhzpPsk4HqF1rbVhB"),
    pubkey!("8AFBJ74rYMFPUY4dD2LZ7XYcd13encGFYH5H5Z69StmV"),
    pubkey!("ABAe8KgEuRuUQodhZf5TDRNu3gDMsGA1PKSPkdcTZpDq"),
    pubkey!("5Gxxxr4tDSoCUG7ote14PDZyQkU4CNCajbGGsFa2DDgh"),
    pubkey!("2sZtTK8VuSD4Lx1ufuHzmex51RPKwqKST2wL2AuqYaao"),
    pubkey!("4gGj3K8Apz28oK2vSCmnGyxqwN9HSvy7uGd1JMdVv9Ki"),
    pubkey!("Aoc6PCf4Z2SZ2ZF63tWgRF6iu5A9sKLBCHc3o2voKc4s"),
    pubkey!("HKCr15tKHtUvU8TncQsYCy4UnJnbX35K1WW5QjEv7Uts"),
    pubkey!("DN5c5py4xKBXJdH54KH4Sx4PWWeNGdSiM1DEvuj6rKRP"),
    pubkey!("J9mgkgVgyzzX9MyrUv5TWxDWCntgTDstPBizMdbgKnCu"),
    pubkey!("7cQfu1fc7Ap17tPGGqgyRLobV5PmXMeoz6CE3LAFbMS6"),
    pubkey!("98YN3hM7hCAk5r4WvXTSAvNoQpevBBQzp7jMFqfRwu2y"),
    pubkey!("F3BBAVn2ZF3wUoFSscCxTVCbpKpMVvsYmMVeWvM9rKZL"),
    pubkey!("6MbyB6tTzfAoJ4Wf9rUzx5FKshK3Zcsh3LpYirvxpc8A"),
    pubkey!("B38bAHBG5s2qFbrE9UBsvqFZpxUovsPmQs6ZinG5iRkT"),
    pubkey!("2KeHF6AtyzizF1gGc5rmJuvNoF3ecvAJoPEuNmFJeoXV"),
    pubkey!("5XGdsYZ2zop8erim7NzR8VT1aBZPgw8RNLvCbCSPtG5S"),
    pubkey!("EiJhZfFcfDHfU1jtwfWoF2BtDph31YCZe94KXoVicQEs"),
    pubkey!("HKU4pHT5p9DundJ34HsxTF2SWroZgMZ5u4JZjH1eT7Q5"),
    pubkey!("2tW6S22dj3La9iMLBxvVbELefodwMFuqke4MoKeC31h9"),
    pubkey!("D2gC8QVrnm79RqodM5ya2ohpuNKgPTQjDiamBkUdN6kr"),
    pubkey!("FBULz76xWgudh1tKpa6Da8BbpWtmWTAG4J7a6jwiYTPF"),
    pubkey!("FCjH2E82Ty9ae2vaE2eQK2YXN4AqKKKJspLCauLpN78a"),
    pubkey!("6HuJjLDAh1WSJXqqbif8GfmrAynFwFirVGkduEknS7pu"),
    pubkey!("35sQFUPELYDCuLe33jhFVPRK9yQ2fW9kYQiD8moNyzHJ"),
    pubkey!("FRZNtP2eDub9sa5hAE13HqKGaAgWWwtdL5DQHQTFh2Zx"),
    pubkey!("2ShzRzBWULvbBcMBjhTTvU9rr7hrtBVwbm9C8f2481vo"),
    pubkey!("54X61uhcugNFZgx3dzH5LugSzN2HTcbDRrqceaZt9hmT"),
    pubkey!("FYGNBUv2ZGgTaS5iGCBCmZga8nGhBYe7Yz3gtKh5peFN"),
    pubkey!("4dWBUggiG4pMoADRv5Fd2jinVfX2i3v6e2d8CFEM45im"),
    pubkey!("DRctBKkEQFU5BqPqQbKDjm1AHAES4UMECoazKb9sXFEJ"),
    pubkey!("5VseA4mZChhKVqgDLC2EWkuPinQkwReubDKwBUDH64qk"),
    pubkey!("3KkqP1nqE11a8z5GnCLFBDfstjCWo6VrdKJQ8cgbmFh4"),
    pubkey!("GDhX6BqGPPTxmphVoCcJd7zzgpVDtTbBeMSD1nzuaBj4"),
    pubkey!("DZTphNHJW6Chn2DJswZZFUF2wnYvDLj7xAk24KSVZTa6"),
    pubkey!("8FQvRZhh4VvkVCScE5ZnydLUczy83cVBM3iUbgzas5co"),
    pubkey!("6vFfX6hYpCH7uyDEvrULme3tPmX19C6vf58YjrLswrb7"),
    pubkey!("2iqppVujiitjEhjefnKprBxo5jJo5JkdDKpi9XPyFSXd"),
    pubkey!("y5vUzHgHxvrDxkoB4wUAVch4kWQ9h6fdVgeegJX2vEB"),
    pubkey!("7pMfEkE1cwiXoUiBDwpDqV3PJ2ksx8LFbo2dqUvjcnu7"),
    pubkey!("8qpLmuTQjQWNGKR8apd14CgK85s5y5ffeBJffGpobQZg"),
    pubkey!("2QdhepnKRTLjjSqPL1PtKNwqrUkoLee5Gqs8bvZhRdMv"),
    pubkey!("2FBrnRJ9rRQk5wn6gevgGUHmw9f6qn8vjKiHFS5Qa6U3"),
    pubkey!("7eysHEAsjVYcz7VdRY9Bd1vdeYprhNLWUQW1bdVhrfDZ"),
    pubkey!("3DH5gYtSgTqvzAkCezBp6So5Jy9uDB9P7unxrzEfjcLf"),
    pubkey!("EDEj1DFGthDq79ry7MNayYdNJD6nGZTgCRVyYV9ED2TE"),
    pubkey!("7jxf2GuqkRUiAfZzBD1NbiZUXowicFBN7gSCgWb7cFnd"),
    pubkey!("4cHjuqmBmKHiSfW861deFWs2Pk4TrSfekhQDSD21wN3R"),
    pubkey!("4Mi3MjutyeTyQUoiwMDfBMV5aAXhatwkUW9zCByK9osu"),
    pubkey!("HSuoJWD4MvhFFyjxaetRQrGzXsk4WQb1PcQ1VSfcYAgk"),
    pubkey!("E6yAjVWa8e7FJrFTGkmiGh8beGNh99gF2rw9UePkC7tv"),
    pubkey!("7Fgnj5bm3Tp6o9GxXF7DExC8Sz4hQF6yoTsGpgXtyJGG"),
    pubkey!("HpgRKX3ZQ9vLa1gZc4b9a1cc5zRyUvqNswgioh8YvWfo"),
    pubkey!("2C5z4i5QZt1BZ94V6Ef5n2wiMixT7RgR5M2f4K44wG1g"),
    pubkey!("4HrmxxrNachNKigwJYh5NfKQ8dT89bfmoXtY1qQbzEGT"),
    pubkey!("4V6idaB1g8g5Hj3KvgqQcBAJMQt3o4PFFzzDcY4Jpga2"),
    pubkey!("5u9yami6L39uHMNtJV43adZnHDEbs5y2wRpGGvAiiUMA"),
    pubkey!("6E8jV592XCNkEMrSEtgZGzX3HykfL9uwQBWRm3ZbzT4C"),
    pubkey!("9kGXj3Ma2wmA5s8MECtdQfsnfsb6WY5uoLki6iLF957d"),
    pubkey!("4LqcUQMrjJWDUv7N7iCF7N9Nwymmkho1Ce1E3aSELWMr"),
    pubkey!("65DHE7gH3XY84h6P3ohxz6xz3Q4xLg2D8ShdU3TrBjbS"),
    pubkey!("3qkfN3yz8vN961VyeLQ8XTGKGpGzc1WTuqhfvSrGoCyL"),
    pubkey!("FPkcKCpfZxFtCAqv5bhTwdmdkogTg3upaGBEiGwfY32F"),
    pubkey!("6vhvtD8H8eHLFPPU2A4httMX1HTcSkHq3ejeMQGYvg5j"),
    pubkey!("7EAY9yJGrvKsZwQ6aKSW8pQNPunz9RaoKgQZLhPFg8Vw"),
    pubkey!("GStheR21jvgEAU435XAyEDbDcmM7EmKRcUjVa8q51k1v"),
    pubkey!("3b7dNJDiZ3SxRyQTFSuJTUsiKf3pjDS6DHPwAxkg6dyF"),
    pubkey!("ExnNyaR1GQ3PNnXjD3ExFwAnYAPinupAKxjLDR3SsQWn"),
    pubkey!("E54n536mzs6hcesC5KJE4qL57aaRe2XP5kBk1bxTqS5S"),
    pubkey!("5NEqg1BcUqbKuYEWXgXPuz3jN9hK3hWwpKXL9PNWGvdT"),
    pubkey!("C3xdTcKnQCkrThWpt98tBt2QPTZsZC87eZEahHDWVjRg"),
    pubkey!("DVzbGu1KfbcSGuGaVciKzNyTnffSN4g32DTGs9RyRYCb"),
    pubkey!("FMcNUEz3deSbCuhUUXFKMUypJr2EeWJiLKFTXnJSpW8L"),
    pubkey!("E55TLy9xrmyfsUZexVuHL3w1PD2tiEdc2zNdbNaZ3TC7"),
    pubkey!("8SUfbCS1ZHtD8952w9HHuWWNqAWCvVN4E1wapLRaZ2ZG"),
    pubkey!("63j8Gikqo72smqqDfovAy98rAkcbakAaBqMQKkVk8GKL"),
    pubkey!("8zPXpXxE6pk3mdfpL2H25zwgdi2Y9SkZiwfT9r1NwTQs"),
    pubkey!("EDviAgJJxJTyJNf8GkKLrpWGF6FSJoa4Vwr6iSnup3Qv"),
    pubkey!("FnArstWLGA8BUhxi7jojPj4nhuYH1XtZgF2JdrQfadBt"),
    pubkey!("G5DKoVXCbhzAthpB4BY6uWfx15CBXMtaFwBGnMbBArb8"),
    pubkey!("GNfeVT5vSWgLYtzveexZJ2Ki9NBtTTzoHAd9oGvoJKW8"),
    pubkey!("DcLQr2ces1zxz4guuuKF2ViGTa3Frv6KgCBxxfX2v84z"),
    pubkey!("AHpBuKHqL2D5cLgSRgM6pNuTR6GgvDok7nZM4Z1nV77n"),
    pubkey!("3FZpboQ2BJPUBGUVKgkGQFkyemJNXAkxEXTm7FnQKCp3"),
    pubkey!("7rA2qcwnX1gH6dRF2bgYsdXagVymFzima86yBgk9ZmEj"),
    pubkey!("FHhFNaSkbnV2cSEG5MahbcHCzQu1gHXNN2fGMLz7KjUE"),
    pubkey!("9j2o8rwxghTFjaYdd3FfvDCo3r5rbu4QfiBUS3nRVLNc"),
    pubkey!("DBVmptnKs1R3hDWvaCnabtW8UvZQrsP9R158rU7JkBkD"),
    pubkey!("EFBQ4y1uc4pp63eHmWmFLzdwRq4UQYH2BWd6dLotqPnw"),
    pubkey!("EHsYgwZXPQRCvdtQPU8T3WpnCu7RoFGaZYiuP7Xzw2PU"),
    pubkey!("3WJ89ynQz1P2m1VzB44wFncKHZcrBGYgL3FGxNyTJQyh"),
    pubkey!("AdLF6h5znsDGSjwRFRzJgn3WP3BgeHQpC3oA2wp3pkri"),
    pubkey!("DSpX3F7ZHrxoc3gdzvciduNsWoby7o8Xp4j1t6Zb8o3u"),
    pubkey!("D3yus6pDJm6Rqk7Q9aonqZ6F4KCbNSkibBibKYE6vUv1"),
    pubkey!("Gia2GT1HV4GXkJD7TWVMQLpnVD7ha6B4iuVrz2aCxwcZ"),
    pubkey!("4WaN7T58Zumfj7Utcmrv545xrPexQFasabUsHxReug6j"),
    pubkey!("79CaF1FPpK6b31DvySi8FpFZCTKfzstVjt3wHgdQ7jmD"),
    pubkey!("HoTuWxCu8Yu9NeXgtakFVBM3isfTJ8T89ikQJJJvYvRs"),
    pubkey!("DPWMFqKD4DkvTW3VrMGLjvFMWFfHrUwGX2KEL2GeHWrj"),
    pubkey!("FZT2aVuWkXz1X8e6NGVr1nTn4abCHZ5AWsGDE7DpWVg5"),
    pubkey!("ExcBWu8fGPdJiaF1b1z3iEef38sjQJks8xvj6M85pPY6"),
    pubkey!("E3BDFtWEMN5c4uikK15pzExQR2SopmHZDeQu1FvbRs7S"),
    pubkey!("9RQMFr6AxzRazjWoZ5ey3vQWjh8t5UsqVhvFnwexJaPi"),
    pubkey!("8Xea6bTgxXUfE3TYpKXxRLN2miNzocmiFoG7FqVcdZz9"),
    pubkey!("ChSYVaJnSYQaBKJw7eKKFNfe3yb93VeRJ38yD6BYKxoF"),
    pubkey!("Eh5mbALuQQZpD9ZGzTfYfuFcmsbWi3dmxLRESYB3KiFV"),
    pubkey!("5EcUrFFrYzzLYHkNqNU6MsNMiNrB3EYQ5fVg6fmWh8fw"),
    pubkey!("AT89Quuqtc6FNFSfWV7WsVhqCGbfiiiWt82tPjQj8tDr"),
    pubkey!("9LQLNcoKd9sRseVz7ZKXaTt69Jzy6X89a51M8eQVRXdM"),
    pubkey!("BGE8uKLnephAecVZ7MhnNsQgopt4MZMAfyDcjN8BFQFk"),
    pubkey!("8cQXCZrpsuwo9CEXyFcf8qWEYVSFKVJQSZGDyAwEYEMY"),
    pubkey!("AQ5Ve6fvgdpTz3ayc4QmTuneydvpCvN2fMGwnZZoh9pa"),
    pubkey!("4dpnfMe3coGHWyU3RaGHUSUXH4s9UC6Fuu6JHTZzEVH8"),
    pubkey!("FravXgS93i3F2TVYnWjWVTqGVcfh5hiWmTrhR7Htb2pX"),
    pubkey!("ESAiTJvAF7Z6t2EjQ1ZxWzMAVPqQYQNb97V4znHR5E7Z"),
    pubkey!("41sCbnyT5JErdvTqN3BBg7kvajZsPZt2rinTqqo5urWK"),
    pubkey!("4MfJ16QzTLsRLRc4HsXvfqnrNQ3Xesd9DMZutJmAAFB9"),
    pubkey!("76Ch9su6yJkULnhxHVAnHxVmsdwBQkMrcHfnHxpxbJ1o"),
    pubkey!("BvjksoRud7ZjfbP9UwcRB6XQgXGRW3UVaNBnjZEZLnfv"),
    pubkey!("HBiSRpcejQeWXxrCYRhngbYJpE9FAUt3bc2Xvi93gwRw"),
    pubkey!("3Wam61QBahZupDp9uHRiaifyoMbmSeEVmVGfsJzbJ9P4"),
    pubkey!("TU5eYJQnvM6rNURtnx5K4MCdRy4QuLurc56cetCpxE2"),
    pubkey!("D1YoMatqc6Qjnr6HC92ZCDy8r5H9Xup4BFw88qNciMNz"),
    pubkey!("HDE4JcuFmCNyKreBaf4hjg6JXLai5RRct2RteACxT5NF"),
    pubkey!("CuGweL2jeoBafJGbroyPK15bg85PqjyYxJfdsxCKe6S6"),
    pubkey!("HCfytQ49w6Dn9UhHCqjNYTZYQ6z5SwqmsyYYqW4EKDdA"),
    pubkey!("CNTZ8xfJDkGRCzyckigcHwLp2k5KR89shpmDfbLL4K1v"),
    pubkey!("44werkGcR5cxW8RsKRJLAFyH5uojAhQUsohonJBGsvwK"),
    pubkey!("6RudGbxLp7qr4Q1Xcz7aeqJ1MnLyfJJG5nixeo8XuW5o"),
    pubkey!("3HRsf5wSorVTzDPeaRh23PsoVnLAoZdu1o8yzziJFTA2"),
    pubkey!("FGMpHNpU5dgqWFkYfk2K5qZrBoYc6fNAeGmc2H6nUQ3J"),
    pubkey!("Ajg29hjynnPxZf2QyKCYVyUzi812f8fFYTZC4fXge34v"),
    pubkey!("3tsVj21QMLPqb3KvU6UcdymWjgqarnkDAMKnz3xksVJw"),
    pubkey!("3STofwwjaoCV7vCCDXCXj7w7v7SdH3uYwC8PmHQPZBwS"),
    pubkey!("C6iYX7jrjV4pH1scscRpa8B8yryd6ViCuHXmBDbD21qa"),
    pubkey!("59gAF4TpfQgra5jSasnC48RSKHuteaXD7iJRjZL2o3Xj"),
    pubkey!("2DDKKPSuxqVKv9zhugFf9Ue344RQ95HwCdH9CnfTFVu3"),
    pubkey!("EU1AU8tUgnGq9od2eN7WcQ2Qj4YoGzcBqdJHKZfNVMAk"),
    pubkey!("Dimv1Q44nUjPvg1vP7P19tG99Us1jzoyiDJQRagfdewL"),
    pubkey!("E5X7mWprg8pdAqBT5HJ1ehu4crAsgUkUX5MwCjuTusZU"),
    pubkey!("5CJHVPwZHMFptVm6RKZF5eVczD9W3Lwb3twnRuaMJXoP"),
    pubkey!("4pCMj9ceL8bbSyJGQVaxp5oSgpGjUB2yuFnBksuGwXC8"),
    pubkey!("4oTzNaLH2w5XB7UYbJ3JnuajqUjqYV2Sm83Nbb7edJdV"),
    pubkey!("WbSqpUDD4Qif3d1W578q6a2f2m12s4D3cA4cjKdCcJo"),
    pubkey!("9rDxSSMVsp7sB8uWuGN9jw76PsDuvphekSWEAzAjJSzj"),
    pubkey!("7S5LqVC1shAGSxCMFfCBuUtZ2gZqSvsvGcKSEfYnN4e2"),
    pubkey!("Ch8PDr9q6Su7JfWM7VjoMfwwWKuKXYtJm7myA39pddNJ"),
    pubkey!("JATjG8nQx8qjWTQLQQqe5ivznrWqpia5FM3VVHs7B3jU"),
    pubkey!("FXVL2dp9RhE5piUvU4KfCLRcXDdbdMBTGZFhSV5xewyt"),
    pubkey!("59yZnShJdpU9JZe1SwqMpaKVBomiGpccoA3CtHnQsjK"),
    pubkey!("7muzUeF367irSvKKgkUx4g8RmjZucFCkCgibFaCQvD2V"),
    pubkey!("A6q8SGzJnbZDT7wtvoNdsTntUFcdMPSAZeVUg5X2v3Sm"),
    pubkey!("APgqvbagVawbWH7uJD2FXKhEw43CyJy6E2EZzLjDqi4k"),
    pubkey!("J5cy5wtwmNTaczYG1fL75NKXaRsVoXphzq3xR7viNJMV"),
    pubkey!("FkCQiE68AhrAsrG9Kau1zKKvBBpxn7buexvFdC3sXF6m"),
    pubkey!("CTpEEdHSXCSYuQMYWKJHxuBmsAx3Q63X3tFuNCnsd6g8"),
    pubkey!("24MaZUkQEFmqsiyn6CN6sgQjW8zQ7MoeAzLNxqFyKKAz"),
    pubkey!("Ho9sGVbu6P4whKMctykQuQdo3SeTbDdj6fnbX4hXCBVC"),
    pubkey!("14LQrjrbQXox9QHsqokBdqtGb98NGnRfa24hmPw2u5gg"),
    pubkey!("725KwV9fpirV1RdRxxWkypRzXQumEFqF6ranuXpZq6sF"),
    pubkey!("6vefU9iJir1tSA9pYFGqcHtb6zbCYAjJdjqbwRFgEtYL"),
    pubkey!("GDCQQGRciE8H5Ywz7qUeABTVH4AxUMMf1DTkJ1ggbQL8"),
    pubkey!("3wbYv69BQrH1ZoV388YbF1YqaGnFG6P1f77ThZzq8nFu"),
    pubkey!("2inqa7HaHKxXxBnW3uTW7q5umFCYZ6vCchuWVotj5CLK"),
    pubkey!("AtK4fYMF1mFosQn6qRiMJoE1hsjpCFwpTy1TKpE5Ez6E"),
    pubkey!("F66LhbNH7uve5r5SPsmFEtLTDTKgSewdqTsaPqvxzuHW"),
    pubkey!("DZ6VV6KRsjSLUMgZZV1DxHMhC297TG8iVr2KqdJo2rSL"),
    pubkey!("9giFuTYRxgUtvpf5tEwBD4MjFRagzLPUqBxzbdK7DWcW"),
    pubkey!("BfhKr9jJEnJYrfkdDGRDuKhBzpP19FWWLJh4pg2XiwXP"),
    pubkey!("ARqohNAwVzBUTrD9H7C895FMvSZDtai3yF3DzXmoTifW"),
    pubkey!("4zUqGss1Rs9wMpSH8Kwvryh2hEPRrZTjtNZLiShww6ub"),
    pubkey!("Dsr4Aa7mcLis1nT7VpDQp8dk3owG84G6PCDzy1w5QJCq"),
    pubkey!("FPMDqAJDqMMcqiKeBY71JR7Nd3ypbHZEwREB1tM8SLvu"),
    pubkey!("3vncnW3swvo3gH6bCUop6pmdVsm8WWRxNNcCAH8FbKqj"),
    pubkey!("eRTFewmjCvcA69mK8p2reKdDUgRfHehiSWw9LnNrbPW"),
    pubkey!("8237aoM8eWMdyjg1dz96iSZAQZq1JQ84przuySMMa4Rg"),
    pubkey!("GbUZn9N4Jsvg5L4FCvwcqC8btyGBSVdz7EQ6AKVDSKUB"),
    pubkey!("6ftyeKVeFY3h83P6j76BcqiQXkzX1qyvyQohQRP5e1Z8"),
    pubkey!("Bg3owHZJdinuivoCw5DaK5nCKgUncHuxvrSaEJCEvpnW"),
    pubkey!("jchaGznN4ddoesbeFBGtCjcpVGYKm5Eoga1ss4YqnsY"),
    pubkey!("A6WPXSQuBFTGh54HvhCU4JvK3NWZiEicD1DE4gwXV2PN"),
    pubkey!("CGvY8owQzfD5ZdZUFATFj5ASJNWz1P6J7PpS1XjFMYRW"),
    pubkey!("5teJJs3NBP6st5jMPqVLDcApamGYs8eG7LVNzh3FUB5n"),
    pubkey!("35zWBXv2qpPjBk1PjcrTaDHmAyJQrJbEpc6Q8p735ar3"),
    pubkey!("EkSVmV25KcPuCRgVD5Dx97mXhuavkCjbv3NFaKmpN1fT"),
    pubkey!("Bke3HsEqAGyLAj8PXFJKPjr3dP7QQUAb3XdvhKGvycEf"),
    pubkey!("667ecUS7wJSsAKDiggLvG329aYdQdEsFmpkwvAukogpG"),
    pubkey!("3d7uQJsjLujEFPs1DJ6TZJnM3beSTtzqnV8hnZCQmFEj"),
    pubkey!("62bwHLXWBRBtfcpKep7WU1VKU6m1cc9d4JCofgma9zqo"),
    pubkey!("61R1ndXxvsWXXkWSyNkCxnzwd3zUNB8Q2ibmkiLPC8ht"),
    pubkey!("5HkQsCFp49JuZFbvdu7tBNgaLJXToeZRcNdvMLwnFdLt"),
    pubkey!("Hmi9EUCMo1n1DxaDBU2nkxczZuJaWVxeQ1vTVbm5GrwL"),
    pubkey!("2Tb76T7Y12ntKYsVEkMUnkXvmF49xg4jgFmrbr6aCaD9"),
    pubkey!("AZDYw6odwVg5fy5GEXL5Pw5EzWeTodSZQ3MDCkHAgVWk"),
    pubkey!("88FTMUzphS7uTGPpA1fHkyPmQHY18MpBPw2sGTSNVX6h"),
    pubkey!("HM9Db4z8dRAtEqAYQvMNDdppdczMyrYpTNQCVngzLmkH"),
    pubkey!("Cw5Ks5JeZzD2b4wsvciQqvKSk4j96H9FRjCpScGkCNm1"),
    pubkey!("5hF1BYigqTC22ptg7rHceiYpyVBRE6wvRj6rYEXvbtTf"),
    pubkey!("5A34M17pS4rSrpcULCcoJvJsBBLX6H1qNEaCfNbyF4Jc"),
    pubkey!("FRHcmSy1AojpnaBvJTfuU648aoc7VMGcKfZmTLeAu5vM"),
    pubkey!("Az74EKYJubLuPgTEJT66iuxqSiEWxU8kj1TE2EPBdrDp"),
    pubkey!("D8vSKqqPayoK59Dk1wqM4HvqLqtafZuhTMQaV8A46oSu"),
    pubkey!("DQavQdYG5jatiG65cedje43enUkh5UTTwKr9Gogzrp9n"),
    pubkey!("FZp6tYRJe8M9DakUvNtteqi2qTPnsyLrDqBr84tgt3Bk"),
    pubkey!("FqRoBY9sKqg17hpaWavbXZB9TPJaoJWqksuv5uGcWjA2"),
    pubkey!("HMGqqvYjBvLsaDbJMFNFW8CtpbVPX6EkRCC9Q6k7hxXM"),
    pubkey!("HNSwfVVzSWSHfePM82iBxguTvRsxqwVhzAHR2A7b5MYj"),
    pubkey!("9kHAjbPP13rtXUNy8xLC1om5ftpbjm8WSuDWt6Jjssn6"),
    pubkey!("57qgdvjMenPP1kb38uTtVkJNP4htPpZfZCPoYfhVTSyL"),
    pubkey!("GbV2e4Es4CzzWU7atyUK5CJdPrVHUvEmeH5qGir72WCw"),
    pubkey!("5u8QznvoHMKMPRPbPsSbuUeTY26idovrz2ReMRQWxSrF"),
    pubkey!("36EZYwvA783R46gMtL4RYBy5yDF5qdKmfTqsUmuMECfD"),
    pubkey!("26EtuskJpdaJW21m5D1DTwig4wfqan6sufNUPWATc6P8"),
    pubkey!("AoN15DRnFvgijamDnQUqevK9sDSFpQS1cnkuA82Vj643"),
    pubkey!("DWDAKY744n1JQ837AmBYYzw6pmLiAc9CTY1xyfWeAFe9"),
    pubkey!("DFeWxSSPL3mNweA8pwoE5SDecp73ZQvv7LcFVx4imGuK"),
    pubkey!("DW4snSr6f2W9HmWMww3Q2eX6JVi5TsGi6cqNpKQAuMpj"),
    pubkey!("3vTShBqiJSeLraEBXzHFN2s882HM5yKBHEWdqGgaq8fa"),
    pubkey!("HepYC6U3pj5syMnaPvfk4HkDj54TSCieuF77vDmU8kVb"),
    pubkey!("9gaD3cgfVWEifh4Fk6qPywkM4uittTereQdtcMwFGqNT"),
    pubkey!("GCzgTiMk1amQfYdUeX6RPCUd9wZj9SLzWqXQhRyEQEcq"),
    pubkey!("62nMWTwcwXCwoaAuiJnnptLL8XbJgo2XCzV8hMF8gRsk"),
    pubkey!("6oqW4b9xH5mA6RRET6MBPWYDnHh7WgNrYipiXeRTba6Z"),
    pubkey!("CHgUFTeVXD435L4E7fcK5udbtJwKCjciUKa5zU5EW3Wm"),
    pubkey!("BCx169cwcg4RnMqCdU7Z9eo6FeE1dbRJfwA6WFTRyaE5"),
    pubkey!("G5jkwDvmoS5dPTtf43qkcLxpYHaSf79Q4LLptNQAb37E"),
    pubkey!("Hy3Fv19NafayLsZZREVKCjMmNqk1cSQUm61Vtf7BRZPB"),
    pubkey!("4QC2imratpEtaLX1ojwxBLnTty6XUpUaXSJEi9GPcVX4"),
    pubkey!("DJvPFKDwspZxai5usNeqZs14akG7TKmQ2DVDy3qVaUUF"),
    pubkey!("FNPsCEriBs2HXrD5QsLrs3G5wfBGymi6yAJb7r5dsKYM"),
    pubkey!("GVLZ5n1Vbkgxe9rv1s4XfN5JWd6QMEDjwv7wMj1BNeEz"),
    pubkey!("8V8Z3MKd5gw66gVy96pkpDF2qtadL58uAU1j5utRxDhw"),
    pubkey!("82pU8g2jQDyDfuH5nmCgFANS4vKTv6tPXe4WBDHRRyWh"),
    pubkey!("GRqiCqqxvocjyPNYnvscKXiUfyokeTz1r6QdXmVPG4qi"),
    pubkey!("cZZbTYKFyB1BLqEgn2kqkFNhS6wuTgQXKhecPKASYJW"),
    pubkey!("EKyJTb3PRHbEe2pA9cGQYMwrZVVwCvnywZj9HGangcgC"),
    pubkey!("ysq96dVZrrMVRassYB2Vr5cHFWoSQDRzQRGNmRRtr1L"),
    pubkey!("JBZiTvcJ38EXAsPm5uZM7UfmqEAALzPpJEiPSafZydrK"),
    pubkey!("4PhLRbZW7UsdB9wrpE52Pdsb9MT8AbyprEjqKxvnozSD"),
    pubkey!("GyBXNtJTrCiPaYDiBDNVhRdmpdChWKCzn4KpEF1UHoyM"),
    pubkey!("3NeUgARDmFgnKtkJLqUcEUNCfknFCcGsFfMJCtx6bAgx"),
    pubkey!("2FQcYfwGp2TwhcP6yLuaY474CHXWbzUft3pvgSH1yzwd"),
    pubkey!("FTu2vPq5wtVzE6fnsTnucNozNSXTZpheCqSH6hwrQK69"),
    pubkey!("3k2FoFw82P5rwStbQV9PTNJmmYGWqZBx9nhU5EgxqTvc"),
    pubkey!("7nonpjyKv4aGgy7uYTAKhcDCpym959NKkiFjrLnSLGp"),
    pubkey!("5v7P6gi5MfVRcXhfna4D2ZT2czQsNGSuQyQEjGiViL4w"),
    pubkey!("DjuKuJo7bewhynRLe6H7mGnWL2mxDkzMzyjE35yKFGn1"),
    pubkey!("Dm9g8QmtTowcsXoVFC51jTUpGLMeSSeDZ8VBLbof6jAx"),
    pubkey!("H6KDdFq2gcodi2YZL43NUe1ozDM4kg5ww3FcZ2yYUXg7"),
    pubkey!("FukYCAkCpN7Vo1brKiHPsRBHN3zp6LYDESYA9Ei1wRWd"),
    pubkey!("2Gz9DeWuGiNgv5rADYk8X7n9pJB5wrPkfiy2mfthZTqq"),
    pubkey!("8iHGN6uzdHMseV5AVJ8jsGNAT5SUZZyfEsz8GXyrTJki"),
    pubkey!("5SsSvmmC5pVNGL58ohr9MRorVSP7B7cHZtSmkmpfvdcY"),
    pubkey!("3bwqEQzikNHm2aTDtUwEHQxHrXe5ENZ17i2iRhazKAzY"),
    pubkey!("53EDRtmiqo3sdPFySmFiaYQwuhynnqvoewjDZQD9Mszq"),
    pubkey!("HfxkwdfyfGEUvxnmHCWUUq4PHSH3wjY5hZTLkAsz7cei"),
    pubkey!("D6Eb5ALf1fX1JRbDeWMJ3kUan4zGLXypccFwyBKgEyc7"),
    pubkey!("6kT4MhDqKrkWikaGpFCvYsk45BUKXEe2gTpNGAR1YcjS"),
    pubkey!("41MN4K6CvuffYWTPZwc7SpgLNM3ZYJdN88DyspoPczKQ"),
    pubkey!("AznVj5ptNVEQwswcnUCE2un6n7aFNvJrdS88Lg4DVMgk"),
    pubkey!("B2PPZwmdNqm9583LfYdbxWCt3KE9DE9NrepiPtjKsa3N"),
    pubkey!("Ar867ACLNUwyHrzmVgEkGfHNvBrb5XVM5MFmMKb5qwR"),
    pubkey!("6jazsPCRGDY9RPkhoJunkWVM8rEQTwpQPCNJjZv6EKTC"),
    pubkey!("DCxFKYf8tswPHQ88HjzSVfnvwf6o3W5npRLioU8fvue9"),
    pubkey!("Cv38sL4cj7rdAgd1aWFE7vKbwXkXriayuCn557FMG9Ps"),
    pubkey!("FHsBXQ4VRmKrFv9UkPFo9T8fNdYgdvgT5CpXbgpdxk18"),
    pubkey!("GnTVSDbdiXRZmKQgYgF1sa53J2Lt9NTHhcYSJJKeZ9sh"),
    pubkey!("E47DKPF7WsxzWjRFcoqZYS4sArQzuTLCzHwqZh4Vjmsp"),
    pubkey!("GNxXr6ShCo7UumkRZmputtTPNo44f3nao6p84hXdw83H"),
    pubkey!("Ep8Let19tG8qdvYbEFvXu7LZubjPmzQN9xsvkhSsvxpp"),
    pubkey!("EEPDXWA6A4x78xWGT8fjJ9KgUMcpgrWDCSo55dvg4j58"),
    pubkey!("BGMYUGDDaiT793rwR5XeF7fVuLn1Mp5YDhV9R6dudQBK"),
    pubkey!("23dhWVadFxas4yy7H8wtHLtqS3fheyNN2cd38cUCv387"),
    pubkey!("767jVSJ2kEeBVsT6sPinf1GfXou7twn72VAWeRtkHJ2q"),
    pubkey!("EVTV8zR8GvskRpaPEFgqww5m2bFDNKyUapazsoMY4MXX"),
    pubkey!("A4cppaH3qhczXjh1ppUYDrXU1GUyhLeWD1hFcWNyGtcX"),
    pubkey!("zthXCn85veXVicaYadQ1HWj7Z5djiBWBB6N327iJ9Qa"),
    pubkey!("AXvGqtQnkpaWRVQfjVFuCcqbBdDCgvB2aeM9m4SV1WTr"),
    pubkey!("92TuQq4JC2YRLc6obJ3ic6hBiKrXj728okuu1L9H6yTt"),
    pubkey!("HaLFpbmwzfW2T2rCoWXpZ5HUpmpEfG9tkBaZdhzUPU2G"),
    pubkey!("Gwd3eRLkCgd6qrLYo9L9pMF6pBjoewP5LG4XzjRct15z"),
    pubkey!("CWaS5vn8CcANNaF3Jm6CGkXWBbUJhbwCH8YNRSgegpbQ"),
    pubkey!("6ZXf9HFnv1h56cYmSb8PsNBx22Hbfux56aEYdT1eVdUu"),
    pubkey!("2NK3cUzYw8CLmgngdABzsWc7WFwNQ2rnCKBM6iUzkzXa"),
    pubkey!("8vAZqfGT2z3kHXsWpqBT2nJHXyjmAv5VWGmK5qaYwEpq"),
    pubkey!("EYCutu46JecJVxJoJWJv3npGJfuQaY9XPbNqvXwsPrBV"),
    pubkey!("HXmWFCsDCsEnmDgVu6wbGmM6fyyotZgC9RUqtQ96KHR7"),
    pubkey!("FzQvLbL5ndb9ukETt2Z9qFi8CmqAKA3SErCfQmgSVB1F"),
    pubkey!("3Q9WTpXN53gsp5WFAtVM1DWnnvRbCgKNjGFjWjWRj1w9"),
    pubkey!("AzFmXV7HhCwZA9BmM7GQFjXMdn46wFb23tBNxhZgaxH4"),
    pubkey!("Adik51SKHNyNhFigBLsuYicgbUtBLS6Ng8co5YJL99gp"),
    pubkey!("CouVNYHueAqnho58b1fLPW7uacKTLX2GeLzGK88VzyEY"),
    pubkey!("BKahqCMQkY7fGH2ycVdmyLeyAv6jDYbgRsm6xyFQm3gw"),
    pubkey!("9YdnGSWFL6X45AR2n3vxdC6M7uBE1wY3FEkDTSEzBX4V"),
    pubkey!("6q4FnEn4zX9iGkHSFRbvUwar8SvfqAy2EMU8c4jwMAar"),
    pubkey!("ARoD6cLmhpuNz4UK2uy2PCji1b9shJmGx8np2CeXZSDB"),
    pubkey!("7Crv72y2HeQc77nKZibJUXZQWeavQzCs9As7KaXiggAE"),
    pubkey!("Bk3s2DZxdcncayAC8m5xuT5NxVJZkLFf9doR6JKJQPs9"),
    pubkey!("3t7X7ebdYK3RfTurta9VyVoArCXdDwg3bAfDtoikmQj7"),
    pubkey!("Hj18cr8WegQLz4Hkadu7ZP38kKWTyUstNcKimY6G4U57"),
    pubkey!("tqCiSbcbr1QKCv7fNA3gaEgCzTgy8acoghebPhN9XzU"),
    pubkey!("EVbJSM1FAXZdAyFyjkmmMSbR7JsPuykg1nPV4kqCBtNF"),
    pubkey!("C4sxLGG96LNj2Na77CGgK3GNdDEkKyGSsu62LXMwGvUH"),
    pubkey!("2TS1jJsf6L9E3SwrHPCTqmYVMZyd9uvDEZKLCKLPcb1m"),
    pubkey!("9NWgRn31g251Reh7At5Ywm5azhabEc7iEvD7jco18LV3"),
    pubkey!("J1MxXSHYpyfEM3B7LusTMrqPQuYSJZ9fA8FP233DwgrF"),
    pubkey!("FG8PeKEmbNcowkourf6MshGPhQkaoiny7GjV3XzQ6Nxe"),
    pubkey!("6jD2qFmkpHpDMgc9vsKLWDnaBd2eqdjpTtETsd8VNrV3"),
    pubkey!("DyvS1KjwbnH5ESsxr84NYH8r7VK24D2avAptJmRfifaZ"),
    pubkey!("23M9Gd27PtyW9AmiJn84tQL1ceadvNMyrhWCKoYeEoEL"),
    pubkey!("Ht2zaPL1Y2CqTbn7gFUjssAjyy8ydXEge7jqqeKaMoep"),
    pubkey!("5B3uhu2eN3UBdqbyFDwChAD9v5AV6RxcaZrXy445BmKK"),
    pubkey!("3PszjX6pPgUNJvGspgdwAsyrfeG69B9sQ9ZcvcGWLD4k"),
    pubkey!("7UkDBUk2ViCfrFR5xDkdZRYxt4YatE91YHGiQ9AoLi67"),
    pubkey!("eTzFGGjLqPncd23cyQwk9maSQgQ3SDpPhw7Pr8zHizA"),
    pubkey!("DfECj56MDXCsgCd9f9SaEbCWXRV5DC5CHcZXJWZTTYp6"),
    pubkey!("Am1QAzYx1FKsUA1d1tZ26U9HDy5SwdrTAG2S9iwrgiWJ"),
    pubkey!("2roYX7iDH8ZAXEf3rwiz3ePqZTu6vkds9r2se5toDWtA"),
    pubkey!("GjzDFoy5NzNS25DbiUkxWTbrbAzswryEV6vMTYfh6P1x"),
    pubkey!("4hNwCnrazYih7aZ56Jnv8XEEJEjp818RF7iVwTvQN9bT"),
    pubkey!("SGnPj4eyJcFeGDVRW2RgvjbCYEG7jBgrxwwGpTuf8oG"),
    pubkey!("AHZ111bxfQJsSdQtuWptBPkARi2fz4RLTNWbfxU3p8N8"),
    pubkey!("2yQ8F11E8UqsyRy4uoa9ZqTiXPggXWqwfjtwzrrRQW3r"),
    pubkey!("5wpUUrrgvhqcwZ24x5kHnJgdP2eq1SeZRmb2Qg3Ease8"),
    pubkey!("A5D4jvoRJDWqS1tSSFMbvupcevfiLkjjP8oswiHNnTt2"),
    pubkey!("68PAYR3moBWsE1H1QDuJmtJ6fdasMmcC1J2ZJ53PY3Pe"),
    pubkey!("SHCDy2ozUE5eAnHsyaoFfh9PYXCZYiUqZDedrS2qWgP"),
    pubkey!("97EygTM9oXipmirGhrn8H8hoeDhhXA63wVm5soPXsFRA"),
    pubkey!("32J39ciuaKGAXtHjYUfHhbRqHYavkvQVSxt1RUes1QEb"),
    pubkey!("F55RRNVgfWyU85DFQeVN8mqNteNUSqZ5SNF9hPmNrMX5"),
    pubkey!("2UGmyfk45MEssR2NUPoTfYuWTVACnivxJBrxYmEtnmpz"),
    pubkey!("ApaTjNn1MpGqZKkQHn8SKFhGxYspbxVhrRYfJAwt4oMN"),
    pubkey!("2M3SZqobtyAXcvnrwfg5Q5mBT7KWzXd7hPhcqkCazXgF"),
    pubkey!("A2APxCvKnEoCKXmwHVXK6vyX6ZfuUCWqZ3RwFN2TQ87E"),
    pubkey!("7o49ZAPUF4bU8yzg9XLVsY4qCHfq2VFGKNQhsC35ByJb"),
    pubkey!("CvFvZHnN42CBCDVzQ2qkRW9uM8XhZHn5pWGx1UAE4GDm"),
    pubkey!("6oa4i39QYkcjiGPXqotbyp5FFQzrgwDEEBFVQkfsSVvF"),
    pubkey!("FdjBRWXzieV1Qtn8FLDWWk2HK1HSQWYduo8y4F1e8GWu"),
    pubkey!("47WiAW991PWxFwjf2P8upMPdN6FHDVN6iim1d8DSCm1Q"),
    pubkey!("N7eK6q5c9tMECT3napWBj49ng9hQxzvzTnoWoSh9Gt3"),
    pubkey!("AakMHUvSNj4KdeJcCEdBzWzitpaNTTGU2qSmBKqBzKfe"),
    pubkey!("2f2AEQhuWCpG5Hmc4wHm1GFQ7JLjAvNbUMxQ4pxaUdu1"),
    pubkey!("D5hNtBakWGVj9XBXU4TSNmRD1aqvZeVjB8uiYtJPHPHu"),
    pubkey!("HTAXpi4r7xQ5ZR6UVeiLoEtJxpiLLuig2rZARRdRex14"),
    pubkey!("J9k6q9gdMc7EZtVNNBaD26iMisGZCxQuMh1jmTis76DZ"),
    pubkey!("4LuGwek6Jv4xpGvsQwZXonmLuRhrpHtmKVs95bN9EkTm"),
    pubkey!("ChQUMBrjG3VwnmJwZtBxZ6TrLRU4HEK34nWRa7hGFhhR"),
    pubkey!("D4SeiExHDPYJeqYusXZMc2wpKqA6BHtpP2P5toHBVppA"),
    pubkey!("H6vG9vF61Gvm9n2vCfCuCE9SD2yUVJx4jhKMg3Db4b8c"),
    pubkey!("6g1C9J9Eta7tL5YQTHSwQaGCD9B6PBKmpWE5CVyA7J4"),
    pubkey!("A6HSuFJ2UxUGPuoyMS7z1kSMZZaZZYNVbSmoEKPpcaiN"),
    pubkey!("E5YDWH7DoLRjTjq8qw7Cq2grqugWe19wPn5jQxm7FNhU"),
    pubkey!("6DrrSbfrgj9Yeo1y1NzH2MvPWSkeuhEL3doa3aWaCRts"),
    pubkey!("3Gz1StHHpUh9Z5GAXG2p8rbjP68UPajAxLQs8YmW2W86"),
    pubkey!("9n3dSLrERZQp95dHXywft7xV8D8xnGFLaUHtEhQVaXaC"),
    pubkey!("9Ccr8k7Ve6dbxvWL7ySS58qehF4FY1EvFuHKm988wLwa"),
];


// 处理后的池子信息
#[derive(Debug, Clone)]
pub struct ProcessedPoolInfo {
    pub address: Pubkey,
    pub token_mint_0: Pubkey,
    pub token_mint_1: Pubkey,
    pub mint_decimals_0: u8,
    pub mint_decimals_1: u8,
    pub tick_current: i32,
    pub sqrt_price_x64: u128,
    // T1 per T0
    pub price_of_token0_in_token1: Decimal,
    // For logging/metrics label e.g., "SOL/USDC" (needs lookup)
    pub token_pair_str: String, 
    pub tick_spacing: u16
}


#[derive(Debug, Clone)]
pub struct RawPoolData {
    // 池子的账户地址
    pub address: Pubkey,
    // 池子账户的原始字节数据
    pub account_data: PoolState
}


// --- 价格计算
pub fn calculate_clmm_price(
    sqrt_price_x64: u128,
    decimals0: u8,
    decimals1: u8,
) -> Result<Decimal, String> {
    if sqrt_price_x64 == 0 {
        // Return Ok with zero price instead of error if sqrt price is 0
        warn!("sqrt_price_x64 is zero, returning zero price.");
        return Ok(Decimal::ZERO);
        // return Err(anyhow!("sqrt_price_x64 cannot be zero"));
    }

    let sqrt_price_x64_dec = Decimal::from_str(&sqrt_price_x64.to_string()).unwrap();
        // .context("Failed to convert sqrt_price_x64 u128 to Decimal")?;
    let two_pow_64 = Decimal::from(2u128).powu(64); // 2^64

    // Avoid division by zero if two_pow_64 somehow is zero (should not happen)
    if two_pow_64.is_zero() {
        return Err("2^64 resulted in zero, cannot calculate price".to_string());
    }

    let sqrt_price = sqrt_price_x64_dec / two_pow_64;
    let price_ratio = sqrt_price * sqrt_price;

    let decimals_diff = decimals0 as i32 - decimals1 as i32;

    // Use Decimal's pow function for potentially large exponents safely
    let decimal_adj_factor_res = dec!(10).checked_powi(decimals_diff.abs().into());

    let decimal_adj_factor = match decimal_adj_factor_res {
        Some(factor) => factor,
        None => return Err("Decimal adjustment factor overflowed".to_string()),
    };

    let final_price = if decimals_diff >= 0 {
        price_ratio.checked_mul(decimal_adj_factor)
    } else {
        price_ratio.checked_div(decimal_adj_factor)
    };

    match final_price {
        Some(price) if price.is_sign_positive() || price.is_zero() => Ok(price),
        Some(_) => Err("Calculated negative price".to_string()), // Should not happen if sqrt_price is valid
        None => Err("Price calculation resulted in overflow/underflow or division by zero".to_string()),
    }
}


pub fn process_raydium_pool(raw_data: RawPoolData) -> Result<ProcessedPoolInfo, String> {
    let pool_state = raw_data.account_data;

    // Sanity check decoded data
    if pool_state.token_mint_0 == Pubkey::default() || pool_state.token_mint_1 == Pubkey::default() {
        return Err(format!("Decoded pool state has default mint address for {}", raw_data.address));
    }

    let price = calculate_clmm_price(
        pool_state.sqrt_price_x64,
        pool_state.mint_decimals_0,
        pool_state.mint_decimals_1,
    )?;

    // TODO: Implement a lookup mechanism to get token symbols (e.g., from a static map or token list API)
    // For now, just use mint addresses as the pair string
    let token_pair_str = format!("{}/{}", pool_state.token_mint_0, pool_state.token_mint_1);

    Ok(ProcessedPoolInfo {
        address: raw_data.address,
        token_mint_0: pool_state.token_mint_0,
        token_mint_1: pool_state.token_mint_1,
        mint_decimals_0: pool_state.mint_decimals_0,
        mint_decimals_1: pool_state.mint_decimals_1,
        tick_current: pool_state.tick_current,
        sqrt_price_x64: pool_state.sqrt_price_x64,
        price_of_token0_in_token1: price,
        token_pair_str,
        tick_spacing: pool_state.tick_spacing
    })
}