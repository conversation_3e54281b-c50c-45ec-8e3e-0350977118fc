use borsh::{<PERSON>rsh<PERSON>eserialize, BorshSerialize};
// use bytemuck::{Pod, Zeroable};
// Many fields might not be used initially
use solana_sdk::pubkey::Pubkey;

// --- Enums (Represent as u8 or relevant integer type for Pod) ---
// Note: Full enum support isn't directly Pod. Use integer representation or handle manually.
// For simplicity, we might omit direct enum mapping if only specific values matter
// or handle them during parsing logic. Let's focus on the main structs first.
// --- Structs with `serialization: "bytemuck"` ---
#[repr(C)]
#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, BorshDeserialize, BorshSerialize)]
pub struct ProtocolFee {
    pub amount_x: u64,
    pub amount_y: u64,
}
#[repr(C)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, BorshDeserialize, BorshSerialize)]
pub struct RewardInfo {
    pub mint: Pubkey,
    pub vault: Pubkey,
    pub funder: Pubkey,
    pub reward_duration: u64,
    pub reward_duration_end: u64,
    pub reward_rate: u128,
    pub last_update_time: u64,
    pub cumulative_seconds_with_empty_liquidity_reward: u64,
}
#[repr(C)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>hDeserialize, BorshSerialize)]
pub struct StaticParameters {
    pub base_factor: u16,
    pub filter_period: u16,
    pub decay_period: u16,
    pub reduction_factor: u16,
    pub variable_fee_control: u32,
    pub max_volatility_accumulator: u32,
    pub min_bin_id: i32,
    pub max_bin_id: i32,
    pub protocol_share: u16,
    pub padding: [u8; 6], // Important for Pod alignment
}
#[repr(C)]
#[derive(Copy, Clone, Debug, BorshDeserialize, BorshSerialize)]
pub struct VariableParameters {
    pub volatility_accumulator: u32,
    pub volatility_reference: u32,
    pub index_reference: i32,
    pub padding: [u8; 4], // Important for Pod alignment
    pub last_update_timestamp: i64,
    pub padding1: [u8; 8], // Important for Pod alignment
}
#[repr(C)]
#[derive(Copy, Clone, Debug, BorshDeserialize, BorshSerialize)]
pub struct LbPair {
    pub parameters: StaticParameters,
    pub v_parameters: VariableParameters,
    pub bump_seed: [u8; 1],
    pub bin_step_seed: [u8; 2],
    pub pair_type: u8, // Enum PairType
    pub active_id: i32,
    pub bin_step: u16,
    pub status: u8, // Enum PairStatus
    pub require_base_factor_seed: u8, // bool represented as u8 in Pod/bytemuck
    pub base_factor_seed: [u8; 2],
    pub activation_type: u8, // Enum ActivationType
    pub padding0: u8, // padding
    pub token_x_mint: Pubkey,
    pub token_y_mint: Pubkey,
    pub reserve_x: Pubkey, // Vault address
    pub reserve_y: Pubkey, // Vault address
    pub protocol_fee: ProtocolFee,
    pub padding1: [u8; 32], // _padding_1
    pub reward_infos: [RewardInfo; 2], // Assuming NUM_REWARDS = 2 based on JSON constant
    pub oracle: Pubkey,
    pub bin_array_bitmap: [u64; 16],
    pub last_updated_at: i64,
    pub padding2: [u8; 32], // _padding_2
    pub pre_activation_swap_address: Pubkey,
    pub base_key: Pubkey,
    pub activation_point: u64,
    pub pre_activation_duration: u64,
    pub padding3: [u8; 8], // _padding 3
    pub padding4: u64, // _padding_4 (was lock_duration)
    pub creator: Pubkey,
    pub reserved: [u8; 24],
}
// Ensure the total size matches what you expect on-chain if possible.
// You can add a static assertion:
// const _: () = assert!(std::mem::size_of::<LbPair>() == EXPECTED_SIZE_IN_BYTES);
// Finding EXPECTED_SIZE_IN_BYTES might require checking Anchor IDL size or program logs.
// --- Other structs (if needed, handle serialization based on JSON) ---
// Example: Bin (also bytemuck)
#[repr(C)]
#[derive(Copy, Clone, Debug, BorshDeserialize, BorshSerialize)]
pub struct Bin {
    pub amount_x: u64,
    pub amount_y: u64,
    pub price: u128,
    pub liquidity_supply: u128,
    pub reward_per_token_stored: [u128; 2], // NUM_REWARDS
    pub fee_amount_x_per_token_stored: u128,
    pub fee_amount_y_per_token_stored: u128,
    pub amount_x_in: u128,
    pub amount_y_in: u128,
}
// We don't need *all* structs from the JSON initially, only those directly related to parsing
// the main LbPair account and potentially Bin data if we need deeper liquidity info.
// Instruction-related structs like `Swap` (the event) are less important for parsing
// the pool state itself.

#[derive(Copy, Clone, Debug)]
pub struct DllmPoolInfo {
    pub pool_id: Pubkey,
    pub current_price: f64,
    pub pool_info: LbPair,
    pub token_x_decimal: u8,
    pub token_y_decimal: u8,
}