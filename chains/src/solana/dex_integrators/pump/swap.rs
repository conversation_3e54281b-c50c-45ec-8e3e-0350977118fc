use std::io::{Read, Write};
use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::instruction::{AccountMeta, Instruction};
use solana_sdk::{pubkey, system_program};
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::{Signer};
use spl_associated_token_account::get_associated_token_address_with_program_id;
use crate::solana::dex_integrators::meteora::swap::BASIS_POINT_MAX;

const WSOL_TOKEN_ACCOUNT: Pubkey = pubkey!("So11111111111111111111111111111111111111112");
const GLOBAL: Pubkey = pubkey!("ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw");
const EVENT_AUTHORITY: Pubkey = pubkey!("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR");
const FEE_RECIPIENT: Pubkey = pubkey!("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV");
const FEE_RECIPIENT_ATA: Pubkey = pubkey!("94qWNrtmfn42h3ZjUZwWvK1MEo9uVmmrBPd2hpNjYDjb");
const PUMP_AMM_PROGRAM_ID: Pubkey = pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
const ASSOCIATED_TOKEN_PROGRAM_ID: Pubkey = pubkey!("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL");
const TOKEN_PROGRAM_ID: Pubkey = pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");

const BUY_DISCRIMINATOR: [u8; 8] = [102, 6, 61, 18, 1, 218, 235, 234];
const SELL_DISCRIMINATOR: [u8; 8] = [51, 230, 133, 164, 1, 127, 131, 173];

#[derive(Copy, Clone, Debug, PartialEq)]
pub struct SwapAccount {
    pub pool: Pubkey,
    pub user: Pubkey,
    pub global_config: Pubkey,
    pub base_mint: Pubkey,
    pub quote_mint: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub pool_base_token_account: Pubkey,
    pub pool_quote_token_account: Pubkey,
    pub protocol_fee_recipient: Pubkey,
    pub protocol_fee_recipient_token_account: Pubkey,
    pub base_token_program: Pubkey,
    pub quote_token_program: Pubkey,
    pub system_program: Pubkey,
    pub associated_token_program: Pubkey,
    pub event_authority: Pubkey,
    pub program: Pubkey,
    pub coin_creator_vault_ata: Pubkey,
    pub coin_creator_vault_authority: Pubkey,
}
const SWAP_IX_ACCOUNTS_LEN: usize = 19;
impl From<SwapAccount> for [AccountMeta; SWAP_IX_ACCOUNTS_LEN] {
    fn from(keys: SwapAccount) -> Self {
        [
            AccountMeta {
                pubkey: keys.pool,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.user,
                is_signer: true,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.global_config,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.base_mint,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.quote_mint,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.user_base_token_account,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.user_quote_token_account,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.pool_base_token_account,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.pool_quote_token_account,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.protocol_fee_recipient,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.protocol_fee_recipient_token_account,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.base_token_program,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.quote_token_program,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.system_program,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.associated_token_program,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.event_authority,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.program,
                is_signer: false,
                is_writable: false,
            },
            AccountMeta {
                pubkey: keys.coin_creator_vault_ata,
                is_signer: false,
                is_writable: true,
            },
            AccountMeta {
                pubkey: keys.coin_creator_vault_authority,
                is_signer: false,
                is_writable: false,
            }
        ]
    }
}
// 
// #[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
// pub struct SwapIxArgs {
//     pub base_amount_in: u64,
//     pub min_quote_amount_out: u64,
// }
// #[derive(Clone, Debug, PartialEq)]
// pub struct SwapIxData(SwapIxArgs);
// 
// impl From<SwapIxArgs> for SwapIxData {
//     fn from(args: SwapIxArgs) -> Self {
//         Self(args)
//     }
// }
// impl SwapIxData {
//     pub fn deserialize(buf: &[u8]) -> std::io::Result<Self> {
//         let mut reader = buf;
//         let mut maybe_discm = [0u8; 8];
//         reader.read_exact(&mut maybe_discm)?;
//         if maybe_discm != SELL_DISCRIMINATOR || maybe_discm != BUY_DISCRIMINATOR {
//             return Err(
//                 std::io::Error::new(
//                     std::io::ErrorKind::Other,
//                     format!(
//                         "discm does not match. Expected: {:?} or {:?}. Received: {:?}",
//                         SELL_DISCRIMINATOR, BUY_DISCRIMINATOR, maybe_discm
//                     ),
//                 ),
//             );
//         }
//         Ok(Self(SwapIxArgs::deserialize(&mut reader)?))
//     }
//     pub fn serialize<W: std::io::Write>(&self, mut writer: W, swap_for_quote: bool) -> std::io::Result<()> {
//         if swap_for_quote {
//             writer.write_all(&SELL_DISCRIMINATOR)?;
//             self.0.serialize(&mut writer)
//         } else {
//             writer.write_all(&BUY_DISCRIMINATOR)?;
//             self.0.serialize(&mut writer)
//         }
//     }
//     pub fn try_to_vec(&self, swap_for_quote: bool) -> std::io::Result<Vec<u8>> {
//         let mut data = Vec::new();
//         self.serialize(&mut data, swap_for_quote)?;
//         Ok(data)
//     }
// }



#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
pub struct SellArgs {
    pub base_amount_in: u64,
    pub min_quote_amount_out: u64,
}

#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq)]
pub struct BuyArgs {
    pub base_amount_out: u64,
    pub max_quote_amount_in: u64,
}

// Enum to hold either SellArgs or BuyArgs
#[derive(Clone, Debug, PartialEq)]
pub enum SwapOperationArgs {
    Sell(SellArgs),
    Buy(BuyArgs),
}

#[derive(Clone, Debug, PartialEq)]
pub struct SwapIxData(SwapOperationArgs);


impl From<SellArgs> for SwapIxData {
    fn from(args: SellArgs) -> Self {
        Self(SwapOperationArgs::Sell(args))
    }
}

impl From<BuyArgs> for SwapIxData {
    fn from(args: BuyArgs) -> Self {
        Self(SwapOperationArgs::Buy(args))
    }
}

impl SwapIxData {
    pub fn deserialize(buf: &[u8]) -> std::io::Result<Self> {
        let mut reader = buf;
        let mut discriminator = [0u8; 8];
        reader.read_exact(&mut discriminator)?;

        if discriminator == SELL_DISCRIMINATOR {
            let args = SellArgs::deserialize(&mut reader)?;
            Ok(SwapIxData(SwapOperationArgs::Sell(args)))
        } else if discriminator == BUY_DISCRIMINATOR {
            let args = BuyArgs::deserialize(&mut reader)?;
            Ok(SwapIxData(SwapOperationArgs::Buy(args)))
        } else {
            Err(std::io::Error::new(
                std::io::ErrorKind::InvalidData, // Use InvalidData for wrong discriminator
                format!(
                    "Discriminator does not match. Expected: {:?} or {:?}. Received: {:?}",
                    SELL_DISCRIMINATOR, BUY_DISCRIMINATOR, discriminator
                ),
            ))
        }
    }
    
    pub fn serialize<W: Write>(&self, mut writer: W) -> std::io::Result<()> {
        match &self.0 {
            SwapOperationArgs::Sell(args) => {
                writer.write_all(&SELL_DISCRIMINATOR)?;
                args.serialize(&mut writer)
            }
            SwapOperationArgs::Buy(args) => {
                writer.write_all(&BUY_DISCRIMINATOR)?;
                args.serialize(&mut writer)
            }
        }
    }
    
    pub fn try_to_vec(&self) -> std::io::Result<Vec<u8>> {
        let mut data = Vec::new();
        self.serialize(&mut data)?;
        Ok(data)
    }
    
    pub fn inner(&self) -> &SwapOperationArgs {
        &self.0
    }
}


pub fn coin_creator_vault_authority_ata(coin_creator: Pubkey) -> (Pubkey, Pubkey) {
    let creator_vault_ata = coin_creator_vault_ata(coin_creator);

    let creator_vault_authority = coin_creator_vault_authority(coin_creator);

    (creator_vault_ata, creator_vault_authority)
}


pub(crate) fn coin_creator_vault_authority(coin_creator: Pubkey) -> Pubkey {
    let (pump_pool_authority, _) = Pubkey::find_program_address(
        &[b"creator_vault", &coin_creator.to_bytes()],
        &PUMP_AMM_PROGRAM_ID,
    );
    pump_pool_authority
}

pub(crate) fn coin_creator_vault_ata(coin_creator: Pubkey) -> Pubkey {
    let creator_vault_authority = coin_creator_vault_authority(coin_creator);
    let associated_token_creator_vault_authority =
        get_associated_token_address_with_program_id(
            &creator_vault_authority,
            &WSOL_TOKEN_ACCOUNT,
            &spl_token::id(),
        );
    associated_token_creator_vault_authority
}



pub async fn create_swap_instruction(user: Pubkey, mint: Pubkey, pool: Pubkey, price: f64, 
                                     amount_in: f64, quote_amount: f64, swap_for_quote: bool, 
                                     slippage: u64, creator: Pubkey) 
    -> Result<(Instruction, u64), String> {
    // Compute associated token account addresses
    let user_base_token_account = get_associated_token_address_with_program_id(
        &user,
        &mint,
        &TOKEN_PROGRAM_ID,
    );

    let user_quote_token_account = get_associated_token_address_with_program_id(
        &user,
        &WSOL_TOKEN_ACCOUNT,
        &TOKEN_PROGRAM_ID,
    );

    let poo_base_mint = get_associated_token_address_with_program_id(
        &pool,
        &mint,
        &TOKEN_PROGRAM_ID,
    );

    let pool_quote_mint = get_associated_token_address_with_program_id(
        &pool,
        &WSOL_TOKEN_ACCOUNT,
        &TOKEN_PROGRAM_ID,
    );

    let (creator_vault_ata, creator_vault_authority) = coin_creator_vault_authority_ata(creator);

    let main_accounts: [AccountMeta; SWAP_IX_ACCOUNTS_LEN] = SwapAccount {
        pool,
        user,
        global_config: GLOBAL,
        base_mint: mint,
        quote_mint: WSOL_TOKEN_ACCOUNT,
        user_base_token_account,
        user_quote_token_account,
        pool_base_token_account: poo_base_mint,
        pool_quote_token_account: pool_quote_mint,
        protocol_fee_recipient: FEE_RECIPIENT,
        protocol_fee_recipient_token_account: FEE_RECIPIENT_ATA,
        base_token_program: TOKEN_PROGRAM_ID,
        quote_token_program: TOKEN_PROGRAM_ID,
        system_program: system_program::id(),
        associated_token_program: ASSOCIATED_TOKEN_PROGRAM_ID,
        event_authority: EVENT_AUTHORITY,
        program: PUMP_AMM_PROGRAM_ID,
        coin_creator_vault_ata: creator_vault_ata,
        coin_creator_vault_authority: creator_vault_authority,
    }.into();
    
    let amount_out = if swap_for_quote {
        quote_amount * 10u64.pow(9) as f64
    } else {
        (quote_amount / price) * 10u64.pow(6) as f64
    };
    let min_amount_out = if swap_for_quote {
        amount_out.ceil() as u64 * slippage / BASIS_POINT_MAX as u64
    } else {
        amount_out.ceil() as u64 * slippage / BASIS_POINT_MAX as u64
    };
    
    let base_amount = if swap_for_quote {
        amount_in * 10u64.pow(6) as f64
    } else {
        amount_in * 10u64.pow(9) as f64
    };
    
    let data = if swap_for_quote {
        SwapIxData(SwapOperationArgs::Sell(
            SellArgs {
                base_amount_in: base_amount.ceil() as u64,
                min_quote_amount_out: min_amount_out,
            }
        )).try_to_vec().unwrap()
    } else {
        SwapIxData(SwapOperationArgs::Buy(
            BuyArgs {
                base_amount_out: min_amount_out,
                max_quote_amount_in: base_amount.ceil() as u64,
            }
        )).try_to_vec().unwrap()
    };

    println!("pump main_accounts: {:?}", main_accounts);
    println!("pump data base_amount: {:?}, min_amount_out: {}", base_amount, min_amount_out);

    let swap_ix = Instruction {
        program_id: PUMP_AMM_PROGRAM_ID,
        accounts: main_accounts.to_vec(),
        data,
    };

    Ok((swap_ix, min_amount_out))
}