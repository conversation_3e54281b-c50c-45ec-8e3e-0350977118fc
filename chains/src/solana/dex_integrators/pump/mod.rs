pub mod pool;
pub mod swap;

use borsh::{BorshDeserialize, BorshSerialize};
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::pubkey;
use solana_sdk::pubkey::Pubkey;


#[derive(Debu<PERSON>, <PERSON>lone, BorshDeserialize, BorshSerialize,)]
pub struct PumpPool {
    pub pool_bump: u8,
    pub index: u16,
    pub creator: Pubkey,
    pub base_mint: Pubkey,
    pub quote_mint: Pubkey,
    pub lp_mint: Pubkey,
    pub pool_base_token_account: Pubkey,
    pub pool_quote_token_account: Pubkey,
    pub lp_supply: u64,
    pub coin_creator: Pubkey,
    pub base_token_amount: u64,
    pub quote_token_amount: u64,
    pub base_token_decimals: u8,
    pub quote_token_decimals: u8,
    pub pool_id: Pubkey,
}

#[derive(Debug, <PERSON>lone)]
pub struct PumpPoolInfo {
    pub current_price: f64,
    pub pool_info: Pump<PERSON>ool,
    pub pool_name: String,
}


pub fn get_pool_info() {
    let url = "https://wiser-nameless-wildflower.solana-mainnet.quiknode.pro/da493d659d090e219d06241413c208a86fdee07a/";
    let rpc_client = RpcClient::new_with_commitment(
        url,
        CommitmentConfig::confirmed(),
    );

    let result = rpc_client.get_account(&pubkey!("9qKxzRejsV6Bp2zkefXWCbGvg61c3hHei7ShXJ4FythA"));

    match result {
        Ok(account) => {
            let info = PumpPool::deserialize(&mut &account.data[8..]).unwrap();
            println!("Account data: {:?}", info);
        }
        Err(e) => {
            println!("Error fetching account: {:?}", e);
        }
    }
}
