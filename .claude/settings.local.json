{"permissions": {"allow": ["Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/instructions/**)", "Read(/Users/<USER>/code/rust-project/echoes/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/**)", "Bash(cargo check:*)", "Read(/Users/<USER>/code/rust-project/echoes/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/instructions/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/state/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/instructions/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/instructions/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/routing/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/routing/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/adapters/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/adapters/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/adapters/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/adapters/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/instructions/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/instructions/**)", "Read(/Users/<USER>/code/rust-project/echoes/programs/dex-router/programs/dex-router/src/**)"], "deny": [], "ask": []}}