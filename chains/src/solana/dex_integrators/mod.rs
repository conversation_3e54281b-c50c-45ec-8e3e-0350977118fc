use std::collections::HashSet;
use std::str::FromStr;
use std::sync::Arc;
use borsh::{BorshDeserialize, BorshSerialize};
use futures::future::{join_all};
use rust_decimal::Decimal;
use rust_decimal::prelude::Zero;
use solana_account_decoder::UiAccountEncoding;
use solana_client::rpc_client::RpcClient;
use solana_client::rpc_config::{RpcAccountInfoConfig, RpcProgramAccountsConfig};
use solana_client::rpc_filter::{Memcmp, RpcFilterType};
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::compute_budget::ComputeBudgetInstruction;
use solana_sdk::pubkey;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::{Keypair, Signer};
use solana_sdk::system_instruction::transfer;
use solana_sdk::transaction::Transaction;
use spl_associated_token_account::get_associated_token_address;
use spl_associated_token_account::instruction::create_associated_token_account_idempotent;
use tracing::{error, info, warn};
use crate::solana::dex_integrators::arbitrage_finder::{find_arbitrage_opportunities, ArbitragePath, DexId, StandardizedPoolInfo};
use crate::solana::dex_integrators::meteora::model::{DllmPoolInfo};
use crate::solana::dex_integrators::pump::pool::{get_pump_pool};
use crate::solana::dex_integrators::raydium::clmm::{process_raydium_pool, ProcessedPoolInfo, RawPoolData, POOLS};
use crate::solana::dex_integrators::raydium::model::PoolState;
use spl_token::{
    instruction::sync_native, native_mint::ID as NATIVE_MINT_ID, ID as TOKEN_PROGRAM_ID,
};
use crate::solana::dex_integrators::meteora::dlmm::get_meteora_dlmm_pool;
use crate::solana::dex_integrators::meteora::swap::{build_ix, SwapExactInParams};
use crate::solana::dex_integrators::pump::PumpPoolInfo;
use crate::solana::dex_integrators::pump::swap::create_swap_instruction;

pub mod raydium;
pub mod arbitrage_finder;
pub mod meteora;
pub mod pump;

pub const ANCHOR_DISCRIMINATOR_SIZE: usize = 8;
const BATCH_SIZE: usize = 100;


pub async fn exec() {
    // get_init_meteora().await;
    // local_sol_airdrop();

    // get_bins_accounts().await;

    // get_pool_info();
    let rpc_url = "https://mainnet.helius-rpc.com/?api-key=39cb5a03-d464-4d2a-9955-d2f1a9c4f955";
    // let rpc_url = "https://capable-spring-surf.solana-mainnet.quiknode.pro/fde205e4959f3e5269d1210aa9463420a268ac23/";
    // let rpc_url = "https://solana-rpc.publicnode.com";

    let rpc_client = RpcClient::new_with_commitment(
        rpc_url,
        CommitmentConfig::confirmed(),
    );
    let rpc = Arc::new(rpc_client);

    let a = "oQyX4ui9zje3Bpjh4YAVXwPoaLexdejudTGxUnYL";
    let b = "SsjicFYtPkG8pTRAAJDkL45xJu";
    let c = "RPhqNXzShtZDdrjT8UC9c";
    let private = a.to_string() + c + b;
    let payer = Keypair::from_base58_string(&private);

    loop {
        tokio::time::sleep(tokio::time::Duration::from_millis(400)).await;

        // 并行执行 两个获取pool 任务
        let rpc_pump = rpc.clone();
        let rpc_mt = rpc.clone();
        let pump_handle = tokio::spawn(async move {
            get_pump_pool(rpc_pump.clone()).await
        });

        let meteora_handle = tokio::spawn(async move {
            get_meteora_dlmm_pool(rpc_mt.clone()).await
        });

        // 等待两个任务都完成，然后获取返回值
        let pools = tokio::try_join!(pump_handle, meteora_handle);

        match pools {
            Ok((pump, meteora)) => {
                // 转换成 StandardizedPoolInfo
                let mut all_pools: Vec<StandardizedPoolInfo> = Vec::new();
                for pool in &pump {
                    let result = convert_pump_pool_to_standard(pool.clone());
                    match result {
                        Ok(pp) => {
                            all_pools.push(pp);
                        }
                        Err(er) => {
                            error!("Error converting pump pool: {:?}", er);
                        }
                    }
                }

                for pool in &meteora {
                    let result = convert_meteora_pool_to_standard(pool.clone());
                    match result {
                        Ok(mp) => {
                            all_pools.push(mp);
                        }
                        Err(er) => {
                            error!("Error converting meteora pool: {:?}", er);
                        }
                    }
                }

                println!("sd pool: {:?}", all_pools);

                // --- 定义允许的起始代币 ---
                let allowed_start_tokens: HashSet<Pubkey> = [
                    // Wrapped SOL (WSOL)
                    "So11111111111111111111111111111111111111112",
                    // USDC (ensure correct mint address for Solana)
                    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                    // USDT (ensure correct mint address for Solana)
                    "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",
                    // Add other core tokens like RAY, mSOL, etc. if desired
                ].iter()
                    .map(|s| Pubkey::from_str(s).expect("Invalid static Pubkey string"))
                    .collect();

                let all_pools = all_pools.iter()
                    .map(|x| Arc::new(x.clone())).collect::<Vec<_>>();


                let paths = find_arbitrage_opportunities(
                    &all_pools,
                    &allowed_start_tokens
                );

                match paths {
                    Ok(ps) => {
                        println!("paths: {:?}", ps);
                        for path in ps {
                            if path.steps.len() == 2 {
                                let step_a = path.steps[0].pool_info.clone();
                                let step_b = path.steps[1].pool_info.clone();

                                let bb = path.profit_factor - 1.0;

                                let slippage = if bb > 0.015 && bb < 0.02 {
                                    9700
                                } else if bb > 0.02 && bb < 0.03 {
                                    9800
                                } else if bb > 0.03 && bb < 0.05 {
                                    9750
                                } else {
                                    9850
                                };

                                let pump_slippage = 9920;
                                let meteora_slippage = 9750;


                                // 1. Pump -> Meteora
                                if step_a.dex == DexId::PumpSwap {
                                    info!("Found Pump -> Meteora arbitrage opportunity");
                                    let pump_pool_info = pump.iter().find(|p|
                                        p.pool_info.pool_id == step_a.pool_address).unwrap();

                                    let dllm_pool_info = meteora.iter().find(|p|
                                        p.pool_id == step_b.pool_address).unwrap();

                                    // tokio::spawn(async move {
                                        pump_to_meteora(pump_pool_info, dllm_pool_info, &payer, bb, rpc.clone(), pump_slippage, meteora_slippage).await;
                                    // });
                                }
                                // 2. Meteora -> Pump
                                else if step_a.dex == DexId::MeteoraDlmm {
                                    info!("Found Meteora -> Pump arbitrage opportunity, slippage: {}", slippage);
                                    let pump_pool_info = pump.iter().find(|p|
                                        p.pool_info.pool_id == step_b.pool_address).unwrap();

                                    let dllm_pool_info = meteora.iter().find(|p|
                                        p.pool_id == step_a.pool_address).unwrap();

                                    // tokio::spawn(async move {
                                        meteora_to_pump(pump_pool_info, dllm_pool_info, &payer, bb, rpc.clone(), pump_slippage, meteora_slippage).await;
                                    // });
                                }
                                tokio::time::sleep(tokio::time::Duration::from_millis(1000 * 60)).await;
                            } else {
                                warn!("Unexpected path length: {}", path.steps.len());
                            }
                        }
                    }
                    Err(e) => {
                        error!("Error finding arbitrage opportunities: {:?}", e);
                    }
                }

            }
            Err(e) => {
                error!("Error occurred while fetching pools: {:?}", e);
            }
        }
    }
}

async fn pump_to_meteora(pump_pool_info: &PumpPoolInfo, dllm_pool_info: &DllmPoolInfo,
                         payer: &Keypair, bb: f64, rpc_client: Arc<RpcClient>, pump_slippage: u64, meteora_slippage: u64) {
    let pool_name = pump_pool_info.pool_name.clone();
    let start_swap_amount = 0.035;
    let user = pubkey!("GFi3B9st6Ni6AUqLJ3WGrDEJqRLHG4HGQmqGWeYEaRk5");
    let mint = pump_pool_info.pool_info.base_mint;
    let pool = pump_pool_info.pool_info.pool_id;
    let creator = pump_pool_info.pool_info.coin_creator;
    let pump_price = pump_pool_info.current_price;
    let pump_ix =
        create_swap_instruction(user, mint, pool, pump_price, start_swap_amount, start_swap_amount,
                                false, pump_slippage, creator).await.unwrap();

    let pump_amount_out = pump_ix.1 as f64 / 10u64.pow(6) as f64;

    let param = SwapExactInParams {
        lb_pair: dllm_pool_info.pool_id,
        amount_in: pump_amount_out,
        swap_for_y: true,
    };

    let meteora_price = dllm_pool_info.current_price;
    let meteora_ix = build_ix(param, dllm_pool_info.pool_info, meteora_price, payer.pubkey(), meteora_slippage)
        .await
        .unwrap();
    let compute_budget_ix = ComputeBudgetInstruction::set_compute_unit_limit(1_500_000);

    // 5. 发送交易
    let mut tx = Transaction::new_with_payer(
        &[compute_budget_ix, pump_ix.0, meteora_ix.0],
        Some(&payer.pubkey()));
    let recent_blockhash = rpc_client.get_latest_blockhash().unwrap();
    tx.sign(&[&payer], recent_blockhash);
    let result = rpc_client.send_and_confirm_transaction(&tx);
    match result {
        Ok(signature) => {
            info!("Swap成功！send block hash: {}", recent_blockhash);
            let message = format!(
                "【{}】 Buy Pump: {:.6} -> {:.6} \\n Sell Meteora: {:.6}, \\n Profit Percent {:.4}",
                pool_name, pump_price, pump_amount_out, meteora_price,  bb * 100.0);

            tokio::spawn(async move {
                info!("sending pump -> meteora message");
                let res = notify::lark::send_message(&message, notify::lark::OPEN_ID).await;
                if let Err(e) = res {
                    error!("Failed to send message: {}", e);
                }
            });
        },
        Err(err) => error!("Error sending transaction: {}", err),
    }

}

async fn meteora_to_pump(pump_pool_info: &PumpPoolInfo, dllm_pool_info: &DllmPoolInfo,
                         payer: &Keypair, bb: f64, rpc_client: Arc<RpcClient>, pump_slippage: u64, meteora_slippage: u64) {
    let start_swap_amount = 0.035;
    let param = SwapExactInParams {
        lb_pair: dllm_pool_info.pool_id,
        amount_in: start_swap_amount,
        swap_for_y: false,
    };

    let meteora_price = dllm_pool_info.current_price;
    let meteora_ix = build_ix(param, dllm_pool_info.pool_info, meteora_price, payer.pubkey(), meteora_slippage)
        .await
        .unwrap();
    let pump_amount_in = meteora_ix.1 as f64 / 10u64.pow(6) as f64;


    let user = pubkey!("GFi3B9st6Ni6AUqLJ3WGrDEJqRLHG4HGQmqGWeYEaRk5");
    let mint = pump_pool_info.pool_info.base_mint;
    let pool = pump_pool_info.pool_info.pool_id;
    let creator = pump_pool_info.pool_info.coin_creator;
    let pump_price = pump_pool_info.current_price;
    let pool_name = pump_pool_info.pool_name.clone();
    let pump_ix =
        create_swap_instruction(user, mint, pool, pump_price, pump_amount_in, start_swap_amount,
                                true, pump_slippage, creator).await.unwrap();


    let compute_budget_ix = ComputeBudgetInstruction::set_compute_unit_limit(1_500_000);

    // 5. 发送交易
    let mut tx = Transaction::new_with_payer(
        &[compute_budget_ix, meteora_ix.0, pump_ix.0],
        Some(&payer.pubkey()));
    let recent_blockhash = rpc_client.get_latest_blockhash().unwrap();
    tx.sign(&[&payer], recent_blockhash);
    let result = rpc_client.send_and_confirm_transaction(&tx);

    match result {
        Ok(signature) => {
            info!("Swap成功！send block hash: {}", recent_blockhash);
            let message = format!(
                "【{}】 Buy Meteora: {:.6} -> {:.6} \\n Sell Pump: {:.6}, Profit Percent {:.4}",
                pool_name, meteora_price, pump_amount_in, pump_price, bb * 100.0);

            tokio::spawn(async move {
                info!("sending meteora -> pump message");
                let res = notify::lark::send_message(&message, notify::lark::OPEN_ID).await;
                if let Err(e) = res {
                    error!("Failed to send message: {}", e);
                }
            });
        },
        Err(err) => error!("Error sending transaction: {}", err),
    }
}

async fn exec_info() {
    let float = fixed_point_to_float(2678545113558123378);
    println!("float: {:?}", float);
    let float = fixed_point_to_float(2672125595605892758);
    println!("float: {:?}", float);

    let x = derive_bin_array_pda(-69);
    println!("x: {:?}", x);

    // get_position_accounts().await;


    // let float = fixed_point_to_float(1428);
    // println!("float: {:?}", float);
    //
    // let float = fixed_point_to_float(********);
    // println!("float: {:?}", float);
}


pub const SCALE_OFFSET: u8 = 64;

fn fixed_point_to_float(fixed_point_value: u128) -> f64 {
    // 计算整数部分
    let integer_part = fixed_point_value >> SCALE_OFFSET;

    // 计算小数部分
    let fractional_part = fixed_point_value & ((1u128 << SCALE_OFFSET) - 1);

    // 计算实际值
    let actual_value = integer_part as f64 + (fractional_part as f64 / (1u128 << SCALE_OFFSET) as f64);

    actual_value
}




pub const POSITION_V2_ACCOUNT_DISCM: [u8; 8] = [117, 176, 212, 199, 245, 180, 133, 182];
#[repr(C)]
#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq, Copy)]
pub struct PositionV2 {
    pub lb_pair: Pubkey,
    pub owner: Pubkey,
    pub liquidity_shares: [u128; 70],
    pub reward_infos: [UserRewardInfo; 70],
    pub fee_infos: [FeeInfo; 70],
    pub lower_bin_id: i32,
    pub upper_bin_id: i32,
    pub last_updated_at: i64,
    pub total_claimed_fee_x_amount: u64,
    pub total_claimed_fee_y_amount: u64,
    pub total_claimed_rewards: [u64; 2],
    pub operator: Pubkey,
    pub lock_release_point: u64,
    pub padding0: u8,
    pub fee_owner: Pubkey,
    pub reserved: [u8; 87],
}


#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq, Copy)]
struct UserRewardInfo {
    pub reward_per_token_completes: [u128; 2],
    pub reward_pendings: [u64; 2],
}


#[derive(Clone, Debug, BorshDeserialize, BorshSerialize, PartialEq, Copy)]
struct FeeInfo {
    pub fee_x_per_token_complete: u128,
    pub fee_y_per_token_complete: u128,
    pub fee_x_pending: u64,
    pub fee_y_pending: u64,
}



#[derive(Clone, Debug, PartialEq)]
pub struct PositionV2Account(pub PositionV2);
impl PositionV2Account {
    pub fn deserialize(buf: &[u8]) -> std::io::Result<Self> {
        use std::io::Read;
        let mut reader = buf;
        let mut maybe_discm = [0u8; 8];
        reader.read_exact(&mut maybe_discm)?;
        if maybe_discm != POSITION_V2_ACCOUNT_DISCM {
            return Err(
                std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!(
                        "discm does not match. Expected: {:?}. Received: {:?}",
                        POSITION_V2_ACCOUNT_DISCM, maybe_discm
                    ),
                ),
            );
        }
        Ok(Self(PositionV2::deserialize(&mut reader)?))
    }
    pub fn serialize<W: std::io::Write>(&self, mut writer: W) -> std::io::Result<()> {
        writer.write_all(&POSITION_V2_ACCOUNT_DISCM)?;
        self.0.serialize(&mut writer)
    }
    pub fn try_to_vec(&self) -> std::io::Result<Vec<u8>> {
        let mut data = Vec::new();
        self.serialize(&mut data)?;
        Ok(data)
    }
}

async fn get_position_accounts() {
    let url = "https://wiser-nameless-wildflower.solana-mainnet.quiknode.pro/da493d659d090e219d06241413c208a86fdee07a/";
    let rpc_client = RpcClient::new_with_commitment(
        url,
        CommitmentConfig::confirmed(),
    );

    let program_id = pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo");
    let pair = pubkey!("ByqmYS1msUjp9oEJVxHM3zSMGbWn2pDLJ1th5SwS5BLL");
    let position_pair_filter =
        RpcFilterType::Memcmp(Memcmp::new_base58_encoded(8, &pair.to_bytes()));

    let account_config = RpcAccountInfoConfig {
        encoding: Some(UiAccountEncoding::Base64),
        ..Default::default()
    };

    let config = RpcProgramAccountsConfig {
        filters: Some(vec![position_pair_filter]),
        account_config,
        ..Default::default()
    };

    let result = rpc_client.get_program_accounts_with_config(&program_id, config);

    match result {
        Ok(accounts) => {
            println!("Fetched {} accounts", accounts.len());
            for (position_key, account_data) in accounts {
                let position_state = PositionV2Account::deserialize(&account_data.data).unwrap().0;
                println!(
                    "Position {} fee owner {}",
                    position_key, position_state.fee_owner
                );
            }
        }
        Err(e) => {
            error!("Failed to fetch position accounts: {}", e);
        }
    }
}


pub const BIN_ARRAY: &[u8] = b"bin_array";
pub fn derive_bin_array_pda(bin_array_index: i64) -> (Pubkey, u8) {
    Pubkey::find_program_address(
        &[BIN_ARRAY, pubkey!("5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6").as_ref(), &bin_array_index.to_le_bytes()],
        &pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"),
    )
}



async fn get_bins_accounts() {
    let url = "https://capable-spring-surf.solana-mainnet.quiknode.pro/fde205e4959f3e5269d1210aa9463420a268ac23/";
    let rpc_client = RpcClient::new_with_commitment(
        url,
        CommitmentConfig::confirmed(),
    );

    let program_id = pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo");
    let pair = pubkey!("FEKBRuXEoCSKHjDHHXnrBv76eckpsyKhpHnenW3JPEPM");
    let position_pair_filter =
        RpcFilterType::Memcmp(Memcmp::new_base58_encoded(8 + 16, &pair.to_bytes()));

    let account_config = RpcAccountInfoConfig {
        encoding: Some(UiAccountEncoding::Base64),
        ..Default::default()
    };

    let config = RpcProgramAccountsConfig {
        filters: Some(vec![position_pair_filter]),
        account_config,
        ..Default::default()
    };

    let result = rpc_client.get_program_accounts_with_config(&program_id, config);

    match result {
        Ok(accounts) => {
            println!("Fetched {} accounts", accounts.len());
            for (account, account_data) in accounts {
                // let position_state = PositionV2Account::deserialize(&account_data.data).unwrap().0;
                println!(
                    "Position {} fee owner {}",
                    account, 0
                );
            }
        }
        Err(e) => {
            error!("Failed to fetch position accounts: {}", e);
        }
    }
}


async fn get_account_info() {
    // wss wss://wiser-nameless-wildflower.solana-mainnet.quiknode.pro/da493d659d090e219d06241413c208a86fdee07a/
    let url = "https://wiser-nameless-wildflower.solana-mainnet.quiknode.pro/da493d659d090e219d06241413c208a86fdee07a/";
    let rpc_client = RpcClient::new_with_commitment(
        url,
        CommitmentConfig::confirmed(),
    );

    // --- 定义允许的起始代币 ---
    let allowed_start_tokens: HashSet<Pubkey> = [
        // Wrapped SOL (WSOL)
        "So11111111111111111111111111111111111111112",
        // USDC (ensure correct mint address for Solana)
        "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        // USDT (ensure correct mint address for Solana)
        "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",
        // Add other core tokens like RAY, mSOL, etc. if desired
    ].iter()
        .map(|s| Pubkey::from_str(s).expect("Invalid static Pubkey string"))
        .collect();

    // 对 POOLS 分组，每组100个 然后调用 get_multiple_accounts_batched
    let rpc = Arc::new(rpc_client);
    loop {
        let mut pps: Vec<Arc<StandardizedPoolInfo>> = Vec::new();
        let chunks = POOLS.chunks(BATCH_SIZE);
        println!("chunks: {:?}", chunks.len());
        let mut fetch_tasks = Vec::new();
        for chunk in chunks {
            let rpc_clone = Arc::clone(&rpc);
            let chunk_owned: Vec<Pubkey> = chunk.to_vec();
            fetch_tasks.push(tokio::spawn( async move {
                match rpc_clone.get_multiple_accounts(&chunk_owned) {
                    Ok(accounts_data) => Some((chunk_owned, accounts_data)),
                    Err(e) => {
                        error!("Failed to fetch account chunk: {}", e);
                        None
                    }
                }
            }));
        }
        let results = join_all(fetch_tasks).await;

        for result in results {
            match result {
                Ok(Some((chunk, accounts_data))) => {
                    for (i, account_data) in accounts_data.iter().enumerate() {
                        if let Some(account) = account_data {
                            let pool_info = process_raydium_pool(RawPoolData {
                                address: chunk[i],
                                account_data: PoolState::deserialize(&mut &account.data[ANCHOR_DISCRIMINATOR_SIZE..]).unwrap(),
                            }).unwrap();
                            pps.push(Arc::new(convert_raydium_processed_to_standard(pool_info).unwrap()));
                        }
                    }
                }
                Ok(None) => continue,
                Err(e) => println!("Error fetching accounts: {}", e),
            }
        }

        let paths = find_arbitrage_opportunities(&pps, &allowed_start_tokens).expect("find opp err");

        println!("paths: {:?}", paths);

        tokio::time::sleep(tokio::time::Duration::from_millis(400)).await;
    }
}



pub async fn get_token_supply() {
    // wss wss://wiser-nameless-wildflower.solana-mainnet.quiknode.pro/da493d659d090e219d06241413c208a86fdee07a/
    let url = "https://wiser-nameless-wildflower.solana-mainnet.quiknode.pro/da493d659d090e219d06241413c208a86fdee07a/";
    let rpc_client = RpcClient::new_with_commitment(
        url,
        CommitmentConfig::confirmed(),
    );



    let tokens = vec![
        pubkey!("3NZ9JMVBmGAqocybic2c7LQCJScmgsAZ6vQqTDzcqmJh"),
        pubkey!("JDzPbXboQYWVmdxXS3LbvjM52RtsV1QaSv2AzoCiai2o"),
    ];

    for tt in tokens {
        let result = rpc_client.get_token_supply(&tt);
        println!("result: {:?}", result);
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }

}


pub async fn wrap_sol() {
    let client = RpcClient::new_with_commitment(
        "https://capable-spring-surf.solana-mainnet.quiknode.pro/fde205e4959f3e5269d1210aa9463420a268ac23/",
        CommitmentConfig::confirmed(),
    );

    let a = "oQyX4ui9zje3Bpjh4YAVXwPoaLexdejudTGxUnYL";
    let b = "SsjicFYtPkG8pTRAAJDkL45xJu";
    let c = "RPhqNXzShtZDdrjT8UC9c";
    let private = a.to_string() + c + b;
    let payer = Keypair::from_base58_string(&private);

    let ata_address = get_associated_token_address(&payer.pubkey(), &NATIVE_MINT_ID);
    /* Wrapped SOL's decimals is 9, hence amount to wrap is 1 SOL */
    let amount = 1 * 10_u64.pow(8);

    // create token account for wrapped sol
    let create_ata_ix = create_associated_token_account_idempotent(
        &payer.pubkey(),
        &payer.pubkey(),
        &NATIVE_MINT_ID,
        &TOKEN_PROGRAM_ID,
    );

    let transfer_ix = transfer(&payer.pubkey(), &ata_address, amount);
    let sync_native_ix = sync_native(&TOKEN_PROGRAM_ID, &ata_address).unwrap();

    let mut transaction = Transaction::new_with_payer(
        &[create_ata_ix, transfer_ix, sync_native_ix],
        Some(&payer.pubkey()),
    );

    transaction.sign(&[&payer], client.get_latest_blockhash().unwrap());

    match client.send_and_confirm_transaction(&transaction) {
        Ok(signature) => println!("Transaction Signature: {}", signature),
        Err(err) => eprintln!("Error sending transaction: {}", err),
    }
}



/// 将 Raydium CLMM 处理后的信息转换为标准化池子信息
///
/// # Arguments
/// * `processed` - 从 Raydium CLMM 获取并处理后的池子信息
///
/// # Returns
/// * `Result<StandardizedPoolInfo>` - 成功则返回标准化信息，否则返回错误
pub fn convert_raydium_processed_to_standard(
    processed: ProcessedPoolInfo,
) -> Result<StandardizedPoolInfo, String> {

    // 1. 确定标准化的 Token0 和 Token1 (Canonical Ordering)
    //    比较 mint 地址的字节表示
    let raydium_mint0_bytes = processed.token_mint_0.to_bytes();
    let raydium_mint1_bytes = processed.token_mint_1.to_bytes();

    // 如果 Raydium 的 mint1 < mint0，则在标准化表示中需要交换顺序
    let needs_swap = raydium_mint1_bytes < raydium_mint0_bytes;

    let (std_mint_0, std_mint_1, std_decimals_0, std_decimals_1) = if needs_swap {
        (
            processed.token_mint_1, // Raydium T1 成为 Standard T0
            processed.token_mint_0, // Raydium T0 成为 Standard T1
            processed.mint_decimals_1,
            processed.mint_decimals_0,
        )
    } else {
        (
            processed.token_mint_0, // Raydium T0 保持 Standard T0
            processed.token_mint_1, // Raydium T1 保持 Standard T1
            processed.mint_decimals_0,
            processed.mint_decimals_1,
        )
    };

    // 2. 根据是否交换顺序，调整价格 price_t1_per_t0
    //    StandardizedPoolInfo.price_t1_per_t0 定义为: 1 单位 Standard T0 值多少 Standard T1
    let std_price_t1_per_t0 = if needs_swap {
        // 如果交换了顺序:
        // Standard T0 = Raydium T1
        // Standard T1 = Raydium T0
        // 我们需要 Price(Standard T1 per Standard T0) = Price(Raydium T0 per Raydium T1)
        // 这等于 1 / Price(Raydium T1 per Raydium T0)，即原始输入价格的倒数

        let original_price = processed.price_of_token0_in_token1;

        // 检查原始价格是否有效，能否计算倒数
        if original_price.is_zero() || original_price.is_sign_negative() {
            warn!(
                "Cannot calculate reciprocal price for pool {} due to invalid input price {}. Skipping pool.",
                processed.address, original_price
            );
            // 返回错误，让调用者决定如何处理（例如，跳过这个池子）
            return Err(format!(
                "Invalid input price ({}) for reciprocal calculation in pool {}",
                original_price, processed.address
            ));
        }

        // 计算倒数: 1.0 / original_price
        Decimal::ONE
            .checked_div(original_price)
            .ok_or_else(|| {
                format!(
                    "Division by zero or overflow calculating reciprocal price (1 / {}) for pool {}",
                    original_price, processed.address
                )
            })? // 使用 ok_or_else 转换 Option<Decimal> 为 Result<Decimal, Error>

    } else {
        // 如果没有交换顺序:
        // Standard T0 = Raydium T0
        // Standard T1 = Raydium T1
        // Price(Standard T1 per Standard T0) = Price(Raydium T1 per Raydium T0)
        // 直接使用原始计算的价格
        processed.price_of_token0_in_token1
    };

    // 3. 最后健全性检查：确保计算出的标准价格非负
    if std_price_t1_per_t0.is_sign_negative() {
        warn!(
            "Resulting standardized price is negative ({}) for pool {}. This should not happen. Skipping pool.",
            std_price_t1_per_t0, processed.address
        );
        return Err(format!(
            "Calculated negative standardized price ({}) for pool {}",
            std_price_t1_per_t0, processed.address
        ));
    }

    // 4. 构建 StandardizedPoolInfo
    Ok(StandardizedPoolInfo {
        dex: DexId::RaydiumClmm, // 这个函数专门处理 Raydium CLMM
        pool_address: processed.address,
        token_mint_0: std_mint_0, // 使用标准化后的 Mint
        token_mint_1: std_mint_1, // 使用标准化后的 Mint
        decimals_0: std_decimals_0, // 使用标准化后的 Decimals
        decimals_1: std_decimals_1, // 使用标准化后的 Decimals
        price_t1_per_t0: std_price_t1_per_t0, // 使用计算/调整后的价格
        tick_spacing: processed.tick_spacing,
        tick_current: processed.tick_current
        // liquidity 和 fee 字段暂时留空或使用默认值
    })
}



pub fn convert_pump_pool_to_standard(pool: PumpPoolInfo) -> Result<StandardizedPoolInfo, String> {
    // 1. 确定标准化的 Token0 和 Token1 (Canonical Ordering)
    //    比较 mint 地址的字节表示
    let pump_mint0_bytes = pool.pool_info.base_mint.to_bytes();
    let pump_mint1_bytes = pool.pool_info.quote_mint.to_bytes();

    // 如果 Pump 的 mint1 < mint0，则在标准化表示中需要交换顺序
    let needs_swap = pump_mint1_bytes < pump_mint0_bytes;

    let (std_mint_0, std_mint_1, std_decimals_0, std_decimals_1) = if needs_swap {
        (
            // Pump T1 成为 Standard T0
            pool.pool_info.quote_mint,
            // Pump T0 成为 Standard T1
            pool.pool_info.base_mint,
            pool.pool_info.quote_token_decimals,
            pool.pool_info.base_token_decimals,
        )
    } else {
        (
            // Pump T0 保持 Standard T0
            pool.pool_info.base_mint,
            // Pump T1 保持 Standard T1
            pool.pool_info.quote_mint,
            pool.pool_info.base_token_decimals,
            pool.pool_info.quote_token_decimals,
        )
    };

    // 2. 根据是否交换顺序，调整价格 price_t1_per_t0
    //    StandardizedPoolInfo.price_t1_per_t0 定义为: 1 单位 Standard T0 值多少 Standard T1
    let std_price_t1_per_t0 = if needs_swap {
        // 如果交换了顺序:
        // Standard T0 = Pump T1
        // Standard T1 = Pump T0
        // 我们需要 Price(Standard T1 per Standard T0) = Price(Pump T0 per Pump T1)
        // 这等于 1 / Price(Pump T1 per Pump T0)，即原始输入价格的倒数

        let original_price = pool.current_price;
        // 检查原始价格是否有效，能否计算倒数
        if original_price.is_zero() || original_price.is_sign_negative() {
            warn!(
                "Cannot calculate reciprocal price for pool {} due to invalid input price {}. Skipping pool.",
                pool.pool_info.pool_id, original_price
            );
            // 返回错误，让调用者决定如何处理（例如，跳过这个池子）
            return Err(format!(
                "Invalid input price ({}) for reciprocal calculation in pool {}",
                original_price, pool.pool_info.pool_id
            ));
        }

        // 计算倒数: 1.0 / original_price
        Decimal::ONE
            .checked_div(Decimal::try_from(original_price).unwrap())
            .ok_or_else(|| {
                format!(
                    "Division by zero or overflow calculating reciprocal price (1 / {}) for pool {}",
                    original_price, pool.pool_info.pool_id
                )
            })? // 使用 ok_or_else 转换 Option<Decimal> 为 Result<Decimal, Error>
    } else {
        // 如果没有交换顺序:
        // Standard T0 = Pump T0
        // Standard T1 = Pump T1
        // Price(Standard T1 per Standard T0) = Price(Pump T1 per Pump T0)
        // 直接使用原始计算的价格
        Decimal::try_from(pool.current_price).unwrap()
    };

    // 3. 最后健全性检查：确保计算出的标准价格非负
    if std_price_t1_per_t0.is_sign_negative() {
        warn!(
            "Resulting standardized price is negative ({}) for pool {}. This should not happen. Skipping pool.",
            std_price_t1_per_t0, pool.pool_info.pool_id
        );
        return Err(format!(
            "Calculated negative standardized price ({}) for pool {}",
            std_price_t1_per_t0, pool.pool_info.pool_id
        ));
    };

    // 4. 构建 StandardizedPoolInfo
    Ok(StandardizedPoolInfo {
        dex: DexId::PumpSwap,
        pool_address: pool.pool_info.pool_id,
        // 使用标准化后的 Mint
        token_mint_0: std_mint_0,
        // 使用标准化后的 Mint
        token_mint_1: std_mint_1,
        // 使用标准化后的 Decimals
        decimals_0: std_decimals_0,
        // 使用标准化后的 Decimals
        decimals_1: std_decimals_1,
        // 使用计算/调整后的价格
        price_t1_per_t0: std_price_t1_per_t0,
        tick_spacing: 0,
        tick_current: 0,
    })
}


pub fn convert_meteora_pool_to_standard(pool: DllmPoolInfo) -> Result<StandardizedPoolInfo, String> {
    // 1. 确定标准化的 Token0 和 Token1 (Canonical Ordering)
    //    比较 mint 地址的字节表示
    let meteora_mint0_bytes = pool.pool_info.token_x_mint.to_bytes();
    let meteora_mint1_bytes = pool.pool_info.token_y_mint.to_bytes();

    // 如果 Meteora 的 mint1 < mint0，则在标准化表示中需要交换顺序
    let needs_swap = meteora_mint1_bytes < meteora_mint0_bytes;

    let (std_mint_0, std_mint_1, std_decimals_0, std_decimals_1) = if needs_swap {
        (
            // Meteora T1 成为 Standard T0
            pool.pool_info.token_y_mint,
            // Meteora T0 成为 Standard T1
            pool.pool_info.token_x_mint,
            pool.token_y_decimal,
            pool.token_x_decimal,
        )
    } else {
        (
            // Meteora T0 保持 Standard T0
            pool.pool_info.token_x_mint,
            // Meteora T1 保持 Standard T1
        pool.pool_info.token_y_mint,
            pool.token_x_decimal,
            pool.token_y_decimal,
        )
    };

    // 2. 根据是否交换顺序，调整价格 price_t1_per_t0
    //    StandardizedPoolInfo.price_t1_per_t0 定义为: 1 单位 Standard T0 值多少 Standard T1
    let std_price_t1_per_t0 = if needs_swap {
        // 如果交换了顺序:
        // Standard T0 = Meteora T1
        // Standard T1 = Meteora T0
        // 我们需要 Price(Standard T1 per Standard T0) = Price(Meteora T0 per Meteora T1)
        // 这等于 1 / Price(Meteora T1 per Meteora T0)，即原始输入价格的倒数

        let original_price = pool.current_price;
        // 检查原始价格是否有效，能否计算倒数
        if original_price.is_zero() || original_price.is_sign_negative() {
            warn!(
                "Cannot calculate reciprocal price for pool {} due to invalid input price {}. Skipping pool.",
                pool.pool_id, original_price
            );

            // 返回错误，让调用者决定如何处理（例如，跳过这个池子）
            return Err(format!(
                "Invalid input price ({}) for reciprocal calculation in pool {}",
                original_price, pool.pool_id
            ));
        }
        // 计算倒数: 1.0 / original_price
        Decimal::ONE
            .checked_div(Decimal::try_from(original_price).unwrap())
            .ok_or_else(|| {
                format!(
                    "Division by zero or overflow calculating reciprocal price (1 / {}) for pool {}",
                    original_price, pool.pool_id
                )
            })? // 使用 ok_or_else 转换 Option<Decimal> 为 Result<Decimal, Error>
    } else {
        // 如果没有交换顺序:
        // Standard T0 = Meteora T0
        // Standard T1 = Meteora T1
        // Price(Standard T1 per Standard T0) = Price(Meteora T1 per Meteora T0)
        // 直接使用原始计算的价格
        Decimal::try_from(pool.current_price).unwrap()
    };

    // 3. 最后健全性检查：确保计算出的标准价格非负
    if std_price_t1_per_t0.is_sign_negative() {
        warn!(
            "Resulting standardized price is negative ({}) for pool {}. This should not happen. Skipping pool.",
            std_price_t1_per_t0, pool.pool_id
        );
        return Err(format!(
            "Calculated negative standardized price ({}) for pool {}",
            std_price_t1_per_t0, pool.pool_id
        ));
    }

    // 4. 构建 StandardizedPoolInfo
    Ok(StandardizedPoolInfo {
        dex: DexId::MeteoraDlmm,
        pool_address: pool.pool_id,
        // 使用标准化后的 Mint
        token_mint_0: std_mint_0,
        // 使用标准化后的 Mint
        token_mint_1: std_mint_1,
        // 使用标准化后的 Decimals
        decimals_0: std_decimals_0,
        // 使用标准化后的 Decimals
        decimals_1: std_decimals_1,
        // 使用计算/调整后的价格
        price_t1_per_t0: std_price_t1_per_t0,
        tick_spacing: 0,
        tick_current: 0,
    })
}
