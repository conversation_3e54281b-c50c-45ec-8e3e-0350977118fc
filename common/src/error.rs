use thiserror::Error;

#[derive(<PERSON><PERSON><PERSON>, Debug, PartialEq)]
pub enum DataParseError {
    #[error("Handle Msg err: {0}, key: {1}")]
    HandleMsg(String, String),

    #[error("CError during handshake: {0}")]
    DuringHandshake(String),

    #[error("Not able to close the connection")]
    NotAbleConnect,

    #[error("Bad Request: {0}")]
    BadRequest(String),


    #[error("Error kline value missing: {0}")]
    KlineValueMissingError(String),
}
