use std::error::Error;
use std::str::FromStr;
use borsh::{BorshDeserialize, BorshSerialize};
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::instruction::{AccountMeta, Instruction};
use solana_sdk::message::Message;
use solana_sdk::pubkey;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::{Keypair, Signer};
use solana_sdk::system_instruction::transfer;
use solana_sdk::transaction::Transaction;
use spl_associated_token_account::{get_associated_token_address, get_associated_token_address_with_program_id};
use spl_associated_token_account::instruction::create_associated_token_account_idempotent;
use spl_token::instruction::sync_native;
use spl_associated_token_account::solana_program::system_program;
use crate::solana::local_pm::get_rpc;

// ===== 常量定义 =====
// 程序 ID
pub const PROGRAM_ID: &str = "4hQpPzzD83qHiA35KwB4K9oE1BLmhPAVBHo1R73dnDsL";

// 指令种子
pub const CONFIG_SEED: &[u8] = b"config";

// 指令标识符 (基于 Anchor 的 8 字节判别器)
const INITIALIZE_CONFIG_DISCRIMINATOR: [u8; 8] = [208, 127, 21, 1, 194, 190, 196, 70];
pub const SWAP_DISCRIMINATOR: [u8; 8] = [248, 198, 158, 145, 225, 117, 135, 200];

// 代币地址常量
pub const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";
pub const USDC_MINT: &str = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
pub const WSOL_ACCOUNT: &str = "8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG";
pub const USDC_ACCOUNT: &str = "5GFFRSdJXK5vnsNLjfqYJKgvNcTrVdcXMCFek6Cs4Jbb";
const TOKEN_PROGRAM_ID: &str = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";

// Pump.fun 程序 ID
const PUMP_PROGRAM_ID: &str = "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA";

// 测试账户私钥
const TEST_ACCOUNT_KEY: [u8; 64] = [98, 203, 88, 128, 173, 80, 204, 75, 101, 56, 11, 205, 180, 29, 51, 58, 96, 29, 98, 215, 244, 11, 64, 105, 93, 43, 246, 137, 179, 251, 40, 215, 17, 102, 253, 101, 70, 250, 25, 188, 108, 109, 57, 151, 174, 56, 190, 181, 250, 179, 43, 3, 110, 218, 243, 70, 27, 95, 205, 20, 161, 89, 92, 13];


// 获取测试用的 RPC 客户端
fn get_test_client() -> RpcClient {
    let rpc_url = "https://www.techxk.com";
    RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::processed())
}

// 获取测试用的密钥对
pub fn get_test_keypair() -> Result<Keypair, Box<dyn Error>> {
    Ok(Keypair::from_bytes(&TEST_ACCOUNT_KEY)?)
}

// 获取用户的体积累积器 PDA
fn get_user_volume_pda(user_pubkey: &Pubkey) -> Pubkey {
    let pump_program_id = pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
    let seeds = &[
        b"user_volume_accumulator",
        user_pubkey.as_ref(),
    ];
    let (user_volume, _bump) = Pubkey::find_program_address(seeds, &pump_program_id);
    user_volume
}

// ===== 测试函数 =====
pub fn init_dex_router() -> Result<(), Box<dyn Error>> {
    let client = get_test_client();
    let payer = get_test_keypair()?;
    println!("付款人公钥: {}", payer.pubkey());

    let program_id = Pubkey::from_str(PROGRAM_ID)?;
    let (config_pda, config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );
    println!("配置账户 PDA: {}", config_pda);
    println!("配置账户 Bump: {}", config_bump);

    let config_args = ConfigArgs::default();
    println!("配置参数:");
    println!("  最大路由金额: {} (约 {} USDC)", config_args.max_route_amount, config_args.max_route_amount / 1_000_000);
    println!("  最大闪电贷金额: {} (约 {} USDC)", config_args.max_flash_loan_amount, config_args.max_flash_loan_amount / 1_000_000);
    println!("  最大滑点: {} 基点 ({}%)", config_args.max_slippage_bps, config_args.max_slippage_bps as f64 / 100.0);
    println!("  最小利润阈值: {} (约 {} USDC)", config_args.min_profit_threshold, config_args.min_profit_threshold / 1_000_000);
    println!("  协议费率: {} 基点 ({}%)", config_args.protocol_fee_bps, config_args.protocol_fee_bps as f64 / 100.0);
    println!("  最大路由步骤: {}", config_args.max_route_steps);

    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&INITIALIZE_CONFIG_DISCRIMINATOR);
    config_args.serialize(&mut instruction_data)?;

    let instruction = Instruction {
        program_id,
        accounts: vec![
            AccountMeta::new(payer.pubkey(), true),  // admin (signer, writable)
            AccountMeta::new(config_pda, false),     // config (writable)
            AccountMeta::new_readonly(system_program::ID, false), // system_program
        ],
        data: instruction_data,
    };

    let recent_blockhash = client.get_latest_blockhash()?;
    let message = Message::new(&[instruction], Some(&payer.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&payer], recent_blockhash);

    let signature = client.send_and_confirm_transaction(&transaction)?;
    println!("交易签名: {}", signature);
    println!("配置初始化成功！");

    println!("\n验证配置账户...");
    match client.get_account(&config_pda) {
        Ok(account) => {
            println!("  ✓ 配置账户已创建");
            println!("  账户所有者: {}", account.owner);
            println!("  账户数据长度: {} 字节", account.data.len());
            println!("  账户余额: {} lamports", account.lamports);
        }
        Err(e) => {
            println!("  ✗ 无法获取配置账户: {}", e);
        }
    }

    Ok(())
}



// DEX 类型枚举
#[derive(BorshSerialize, BorshDeserialize, Clone, Copy, Debug)]
#[borsh(use_discriminant = true)]
pub enum Dex {
    RaydiumClmm = 0,
    RaydiumCpmm = 1,
    MeteoraDlmm = 2,
    MeteoraAmm = 3,
    Orca = 4,
    PumpSwapBuy = 5,
    PumpSwapSell = 6,
}

// 路由模式枚举
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
#[borsh(use_discriminant = true)]
pub enum RoutingMode {
    Linear = 0,
    Circular = 1,
    Branching = 2,
    Batched = 3,
}

// 单个路由定义
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
pub struct Route {
    pub dex_id: u8,  // DEX类型ID (使用u8避免反序列化失败)
    pub input_mint: Pubkey,
    pub output_mint: Pubkey,
}

// 闪电贷配置（可选）
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
pub struct FlashLoanConfig {
    pub provider: u8,
    pub provider_program: Pubkey,
    pub borrower: Pubkey,
    pub amount: u64,
    pub max_fee_bps: u16,
}

// 路由配置
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
pub struct RouteConfig {
    pub routing_mode_id: u8,  // 路由模式ID (使用u8避免反序列化失败)
    pub routes: Vec<Route>,
    pub amount_in: u64,
    pub min_amount_out: u64,
    pub max_slippage_bps: u16,
    pub flash_loan: Option<FlashLoanConfig>,
}

// 配置参数结构（与 echoes 项目保持一致）
#[derive(BorshSerialize, BorshDeserialize, Debug, Clone)]
pub struct ConfigArgs {
    pub max_route_amount: u64,
    pub max_flash_loan_amount: u64,
    pub max_slippage_bps: u16,
    pub min_profit_threshold: u64,
    pub protocol_fee_bps: u16,
    pub max_route_steps: u8,
}

impl Default for ConfigArgs {
    fn default() -> Self {
        Self {
            max_route_amount: 1_000_000_000_000, // 1M USDC
            max_flash_loan_amount: 10_000_000_000_000, // 10M USDC
            max_slippage_bps: 300, // 3%
            min_profit_threshold: 1_000_000, // 1 USDC
            protocol_fee_bps: 30, // 0.3%
            max_route_steps: 6,
        }
    }
}

// SwapArgs 参数结构（与 echoes 项目保持一致）
#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
pub struct SwapArgs {
    pub route_config: RouteConfig,
    pub amount_in: u64,
    pub expect_amount_out: u64,
    pub min_return: u64,
}


// Raydium CLMM 交易测试
pub fn router_raydium_swap() {
    let client = get_test_client();
    let payer = get_test_keypair().unwrap();
    println!("付款人公钥: {}", payer.pubkey());

    let program_id = Pubkey::from_str(PROGRAM_ID).unwrap();
    let wsol_mint = Pubkey::from_str(WSOL_MINT).unwrap();
    let usdc_mint = Pubkey::from_str(USDC_MINT).unwrap();
    let wsol_account = Pubkey::from_str(WSOL_ACCOUNT).unwrap();
    let usdc_account = Pubkey::from_str(USDC_ACCOUNT).unwrap();

    let (config_pda, _config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );
    println!("配置账户 PDA: {}", config_pda);

    // 5. 构建路由配置
    let route = Route {
        dex_id: 0,  // RaydiumClmm
        input_mint: wsol_mint,
        output_mint: usdc_mint,
    };

    let route_config = RouteConfig {
        routing_mode_id: 0,  // Linear
        routes: vec![route],
        amount_in: 100_000_000, // 0.0001 WSOL (9 位小数)
        min_amount_out: 1, // 预期最少得到的 WFLI 数量
        max_slippage_bps: 300, // 3% 滑点
        flash_loan: None, // 不使用闪电贷
    };

    // 6. 构建 SwapArgs
    let swap_args = SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 1, // 预期输出量
        min_return: route_config.min_amount_out,
    };

    let order_id: u64 = 12345;

    println!("Raydium CLMM 交换配置:");
    println!("  输入代币: {} (WSOL)", wsol_mint);
    println!("  输出代币: {} (USDC)", usdc_mint);
    println!("  输入金额: {} (约 {} SOL)", swap_args.amount_in, swap_args.amount_in as f64 / 1_000_000_000.0);
    println!("  期望输出: {}", swap_args.expect_amount_out);
    println!("  最小返回: {}", swap_args.min_return);
    println!("  最大滑点: {} 基点 ({}%)", route_config.max_slippage_bps, route_config.max_slippage_bps as f64 / 100.0);
    println!("  订单 ID: {}", order_id);

    // 7. 构建指令数据
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
    swap_args.serialize(&mut instruction_data).unwrap();
    order_id.serialize(&mut instruction_data).unwrap();


    // 8. 构建指令账户
    let accounts = vec![
        AccountMeta::new(payer.pubkey(), true),        // user (signer, writable)
        AccountMeta::new_readonly(config_pda, false), // config (readable)
        AccountMeta::new(wsol_account, false),        // source_token_account (writable)
        AccountMeta::new(usdc_account, false),        // destination_token_account (writable)
        AccountMeta::new_readonly(wsol_mint, false),  // source_mint (readable)
        AccountMeta::new_readonly(usdc_mint, false),  // destination_mint (readable)

        AccountMeta::new(pubkey!("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK"), false),
        AccountMeta::new(payer.pubkey(), false),
        AccountMeta::new(wsol_account, false),
        AccountMeta::new(usdc_account, false),

        AccountMeta::new(pubkey!("3h2e43PunVA5K34vwKCLHWhZF4aZpyaC9RmxvshGAQpL"), false),
        AccountMeta::new(pubkey!("3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv"), false),
        AccountMeta::new(pubkey!("4ct7br2vTPzfdmY3S5HLtTxcGSBfn6pnw98hsS6v359A"), false),
        AccountMeta::new(pubkey!("5it83u57VRrVgc51oNV19TTmAJuffPx5GtGwQr7gQNUo"), false),
        AccountMeta::new(pubkey!("3Y695CuQ8AP4anbwAqiEBeQF9KxqHFr8piEwvw3UePnQ"), false),
        AccountMeta::new(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false),
        AccountMeta::new(pubkey!("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"), false),
        AccountMeta::new(pubkey!("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"), false),
        AccountMeta::new(pubkey!("So11111111111111111111111111111111111111112"), false),
        AccountMeta::new(pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), false),
        AccountMeta::new(pubkey!("4NFvUKqknMpoe6CWTzK758B8ojVLzURL5pC6MtiaJ8TQ"), false),
        AccountMeta::new(pubkey!("3VuuojnJGqwFrPjwfbTD3GLKiiijGvktT9TUubW728W1"), false),
        AccountMeta::new(pubkey!("9KcgZN8TNMv5B873SfKZfrbtkaKzhRaZPZUg1UrveS36"), false),
        AccountMeta::new(pubkey!("3pZjqGBxqUF48bCVyFvbzbWGWtr31ATkFHYL9cMJLVoG"), false),
    ];

    // 9. 构建指令
    let instruction = Instruction {
        program_id,
        accounts,
        data: instruction_data,
    };

    // 10. 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash().unwrap();

    // 11. 构建并签名交易
    let message = Message::new(&[instruction], Some(&payer.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&payer], recent_blockhash);


    // 13. 发送交易 (取消注释以实际执行)
    println!("\n发送交易...");
    let signature = client.send_and_confirm_transaction(&transaction).unwrap();
    println!("交易签名: {}", signature);
    println!("交换操作成功！");

}


pub fn router_pump_swap_test() {
    // 1. 设置 RPC 客户端
    let rpc_url = "https://www.techxk.com";
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::processed());

    // 2. 加载付款人密钥对 (在实际使用中从文件加载)
    let acc = [98, 203, 88, 128, 173, 80, 204, 75, 101, 56, 11, 205, 180, 29, 51, 58, 96, 29, 98, 215, 244, 11, 64, 105, 93, 43, 246, 137, 179, 251, 40, 215, 17, 102, 253, 101, 70, 250, 25, 188, 108, 109, 57, 151, 174, 56, 190, 181, 250, 179, 43, 3, 110, 218, 243, 70, 27, 95, 205, 20, 161, 89, 92, 13];
    // 把 上边的 acc 转换成 Keypair
    let payer = Keypair::from_bytes(&acc).unwrap();
    println!("付款人公钥: {}", payer.pubkey());


    let pump_program_id = pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
    let user_pubkey = payer.pubkey();

    let seeds = &[
        b"user_volume_accumulator", // 常量seed
        user_pubkey.as_ref(),      // 用户公钥
    ];
    let (user_volume, bump) = Pubkey::find_program_address(seeds, &pump_program_id);


    let program_id = Pubkey::from_str(PROGRAM_ID).unwrap();
    // 4. 生成配置账户的 PDA
    let (config_pda, _config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );

    println!("配置账户 PDA: {}", config_pda);

    let usdc_mint = pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
    let wsol_mint = pubkey!("So11111111111111111111111111111111111111112");

    let usdc_account = get_associated_token_address_with_program_id(
        &user_pubkey,
        &usdc_mint,
        &spl_token::id(),
    );
    let wsol_account = pubkey!("8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG");


    let route = Route {
        dex_id: 5,  // PumpSwapBuy
        input_mint: wsol_mint,
        output_mint: usdc_mint,
    };

    let route_config = RouteConfig {
        routing_mode_id: 0,  // Linear
        routes: vec![route],
        amount_in: 100_000_000, // 0.0001 WSOL (9 位小数)
        min_amount_out: 15, // 预期最少得到的 WFLI 数量
        max_slippage_bps: 300, // 3% 滑点
        flash_loan: None, // 不使用闪电贷
    };

    // 6. 构建 SwapArgs
    let swap_args = SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 1, // 预期输出量
        min_return: route_config.min_amount_out,
    };

    let order_id: u64 = 1234577; // 订单 ID


    println!("交换配置:");
    println!("  输入代币: {} (WSOL)", wsol_mint);
    println!("  输出代币: {} (USDC)", usdc_mint);
    println!("  输入金额: {} (约 {} SOL)", swap_args.amount_in, swap_args.amount_in as f64 / 1_000_000_000.0);
    println!("  期望输出: {}", swap_args.expect_amount_out);
    println!("  最小返回: {}", swap_args.min_return);
    println!("  最大滑点: {} 基点 ({}%)", route_config.max_slippage_bps, route_config.max_slippage_bps as f64 / 100.0);
    println!("  订单 ID: {}", order_id);

    // 7. 构建指令数据
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
    swap_args.serialize(&mut instruction_data).unwrap();
    order_id.serialize(&mut instruction_data).unwrap();



    // 8. 构建指令账户
    let accounts = vec![
        AccountMeta::new(payer.pubkey(), true),        // user (signer, writable)
        AccountMeta::new_readonly(config_pda, false), // config (readable)
        AccountMeta::new(wsol_account, false),        // source_token_account (writable)
        AccountMeta::new(usdc_account, false),        // destination_token_account (writable)
        AccountMeta::new_readonly(wsol_mint, false),  // source_mint (readable)
        AccountMeta::new_readonly(usdc_mint, false),  // destination_mint (readable)


        AccountMeta::new(pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA"), false),
        AccountMeta::new(payer.pubkey(), false),
        AccountMeta::new(wsol_account, false),
        AccountMeta::new(usdc_account, false),

        AccountMeta::new(pubkey!("Gf7sXMoP8iRw4iiXmJ1nq4vxcRycbGXy5RL8a8LnTd3v"), false),
        AccountMeta::new(pubkey!("ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw"), false),
        AccountMeta::new(pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), false),
        AccountMeta::new(pubkey!("So11111111111111111111111111111111111111112"), false),
        AccountMeta::new(pubkey!("nML7msD1MiJHxFvhv4po1u6C4KpWr64ugKqc75DMuD2"), false),
        AccountMeta::new(pubkey!("EjHirXt2bQd2DDNveagHHCWYzUwtY1iwNbBrV5j84e6j"), false),
        AccountMeta::new(pubkey!("AVmoTthdrX6tKt4nDjco2D775W2YK3sDhxPcMmzUAmTY"), false),
        AccountMeta::new(pubkey!("FGptqdxjahafaCzpZ1T6EDtCzYMv7Dyn5MgBLyB3VUFW"), false),
        AccountMeta::new(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false),
        AccountMeta::new(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false),
        AccountMeta::new(pubkey!("11111111111111111111111111111111"), false),
        AccountMeta::new(pubkey!("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"), false),
        AccountMeta::new(pubkey!("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR"), false),
        AccountMeta::new(pubkey!("Ei6iux5MMYG8JxCTr58goADqFTtMroL9TXJityF3fAQc"), false),
        AccountMeta::new(pubkey!("8N3GDaZ2iwN65oxVatKTLPNooAVUJTbfiVJ1ahyqwjSk"), false),
        AccountMeta::new(pubkey!("C2aFPdENg4A2HQsmrd5rTw5TaYBX5Ku887cWjbFKtZpw"), false),
        AccountMeta::new(user_volume, false),
    ];

    // 9. 构建指令
    let instruction = Instruction {
        program_id,
        accounts,
        data: instruction_data,
    };

    // 10. 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash().unwrap();

    // 11. 构建并签名交易
    let message = Message::new(&[instruction], Some(&payer.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&payer], recent_blockhash);


    // 13. 发送交易 (取消注释以实际执行)
    println!("\n发送交易...");
    let signature = client.send_and_confirm_transaction(&transaction).unwrap();
    println!("交易签名: {}", signature);
    println!("交换操作成功！");
}


pub fn router_pump_swap_sell_test() {
    // 1. 设置 RPC 客户端
    let rpc_url = "https://www.techxk.com";
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::processed());

    // 2. 加载付款人密钥对 (在实际使用中从文件加载)
    let acc = [98, 203, 88, 128, 173, 80, 204, 75, 101, 56, 11, 205, 180, 29, 51, 58, 96, 29, 98, 215, 244, 11, 64, 105, 93, 43, 246, 137, 179, 251, 40, 215, 17, 102, 253, 101, 70, 250, 25, 188, 108, 109, 57, 151, 174, 56, 190, 181, 250, 179, 43, 3, 110, 218, 243, 70, 27, 95, 205, 20, 161, 89, 92, 13];
    // 把 上边的 acc 转换成 Keypair
    let payer = Keypair::from_bytes(&acc).unwrap();
    println!("付款人公钥: {}", payer.pubkey());


    let pump_program_id = pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
    let user_pubkey = payer.pubkey();

    let seeds = &[
        b"user_volume_accumulator", // 常量seed
        user_pubkey.as_ref(),      // 用户公钥
    ];
    let (user_volume, bump) = Pubkey::find_program_address(seeds, &pump_program_id);


    let program_id = Pubkey::from_str(PROGRAM_ID).unwrap();
    // 4. 生成配置账户的 PDA
    let (config_pda, _config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );

    println!("配置账户 PDA: {}", config_pda);

    let usdc_mint = pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
    let wsol_mint = pubkey!("So11111111111111111111111111111111111111112");

    let usdc_account = get_associated_token_address_with_program_id(
        &user_pubkey,
        &usdc_mint,
        &spl_token::id(),
    );
    let wsol_account = pubkey!("8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG");


    let route = Route {
        dex_id: 6,  // PumpSwapSell
        input_mint: usdc_mint,
        output_mint: wsol_mint,
    };

    let route_config = RouteConfig {
        routing_mode_id: 0,  // Linear
        routes: vec![route],
        amount_in: 17_000_000, // 0.0001 WSOL (9 位小数)
        min_amount_out: 15, // 预期最少得到的 WFLI 数量
        max_slippage_bps: 300, // 3% 滑点
        flash_loan: None, // 不使用闪电贷
    };

    // 6. 构建 SwapArgs
    let swap_args = SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 1, // 预期输出量
        min_return: route_config.min_amount_out,
    };

    let order_id: u64 = 1234577; // 订单 ID


    println!("交换配置:");
    println!("  输入代币: {} (WSOL)", wsol_mint);
    println!("  输出代币: {} (USDC)", usdc_mint);
    println!("  输入金额: {} (约 {} USDC)", swap_args.amount_in, swap_args.amount_in as f64 / 1_000_000.0);
    println!("  期望输出: {}", swap_args.expect_amount_out);
    println!("  最小返回: {}", swap_args.min_return);
    println!("  最大滑点: {} 基点 ({}%)", route_config.max_slippage_bps, route_config.max_slippage_bps as f64 / 100.0);
    println!("  订单 ID: {}", order_id);

    // 7. 构建指令数据
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
    swap_args.serialize(&mut instruction_data).unwrap();
    order_id.serialize(&mut instruction_data).unwrap();



    // 8. 构建指令账户
    let accounts = vec![
        AccountMeta::new(payer.pubkey(), true),        // user (signer, writable)
        AccountMeta::new_readonly(config_pda, false), // config (readable)
        AccountMeta::new(usdc_account, false),
        AccountMeta::new(wsol_account, false),        // source_token_account (writable)
        AccountMeta::new_readonly(usdc_mint, false),  // destination_mint (readable) // destination_token_account (writable)
        AccountMeta::new_readonly(wsol_mint, false),  // source_mint (readable)


        AccountMeta::new(pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA"), false),
        AccountMeta::new(payer.pubkey(), false),
        AccountMeta::new(usdc_account, false),
        AccountMeta::new(wsol_account, false),

        AccountMeta::new(pubkey!("Gf7sXMoP8iRw4iiXmJ1nq4vxcRycbGXy5RL8a8LnTd3v"), false),
        AccountMeta::new(pubkey!("ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw"), false),
        AccountMeta::new(pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), false),
        AccountMeta::new(pubkey!("So11111111111111111111111111111111111111112"), false),
        AccountMeta::new(pubkey!("nML7msD1MiJHxFvhv4po1u6C4KpWr64ugKqc75DMuD2"), false),
        AccountMeta::new(pubkey!("EjHirXt2bQd2DDNveagHHCWYzUwtY1iwNbBrV5j84e6j"), false),
        AccountMeta::new(pubkey!("AVmoTthdrX6tKt4nDjco2D775W2YK3sDhxPcMmzUAmTY"), false),
        AccountMeta::new(pubkey!("FGptqdxjahafaCzpZ1T6EDtCzYMv7Dyn5MgBLyB3VUFW"), false),
        AccountMeta::new(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false),
        AccountMeta::new(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false),
        AccountMeta::new(pubkey!("11111111111111111111111111111111"), false),
        AccountMeta::new(pubkey!("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"), false),
        AccountMeta::new(pubkey!("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR"), false),
        AccountMeta::new(pubkey!("Ei6iux5MMYG8JxCTr58goADqFTtMroL9TXJityF3fAQc"), false),
        AccountMeta::new(pubkey!("8N3GDaZ2iwN65oxVatKTLPNooAVUJTbfiVJ1ahyqwjSk"), false),
    ];

    // 9. 构建指令
    let instruction = Instruction {
        program_id,
        accounts,
        data: instruction_data,
    };

    // 10. 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash().unwrap();

    // 11. 构建并签名交易
    let message = Message::new(&[instruction], Some(&payer.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&payer], recent_blockhash);


    // 13. 发送交易 (取消注释以实际执行)
    println!("\n发送交易...");
    let signature = client.send_and_confirm_transaction(&transaction).unwrap();
    println!("交易签名: {}", signature);
    println!("交换操作成功！");

}

// Orca Whirlpool 交易测试
pub fn router_orca_swap() {
    let client = get_test_client();
    let payer = get_test_keypair().unwrap();
    println!("付款人公钥: {}", payer.pubkey());

    let program_id = Pubkey::from_str(PROGRAM_ID).unwrap();
    let wsol_mint = Pubkey::from_str(WSOL_MINT).unwrap();
    let usdc_mint = Pubkey::from_str(USDC_MINT).unwrap();
    let wsol_account = Pubkey::from_str(WSOL_ACCOUNT).unwrap();
    let usdc_account = Pubkey::from_str(USDC_ACCOUNT).unwrap();

    let (config_pda, _config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );
    println!("配置账户 PDA: {}", config_pda);

    // 构建路由配置
    let route = Route {
        dex_id: 4,  // Orca
        input_mint: wsol_mint,
        output_mint: usdc_mint,
    };

    let route_config = RouteConfig {
        routing_mode_id: 0,  // Linear
        routes: vec![route],
        amount_in: 25_000_000, // 0.1 SOL
        min_amount_out: 1, // 预期最少得到的 USDC 数量
        max_slippage_bps: 300, // 3% 滑点
        flash_loan: None, // 不使用闪电贷
    };

    // 构建 SwapArgs
    let swap_args = SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 1, // 预期输出量
        min_return: route_config.min_amount_out,
    };

    let order_id: u64 = 12346;

    println!("Orca Whirlpool 交换配置:");
    println!("  输入代币: {} (WSOL)", wsol_mint);
    println!("  输出代币: {} (USDC)", usdc_mint);
    println!("  输入金额: {} (约 {} SOL)", swap_args.amount_in, swap_args.amount_in as f64 / 1_000_000_000.0);
    println!("  期望输出: {}", swap_args.expect_amount_out);
    println!("  最小返回: {}", swap_args.min_return);
    println!("  最大滑点: {} 基点 ({}%)", route_config.max_slippage_bps, route_config.max_slippage_bps as f64 / 100.0);
    println!("  订单 ID: {}", order_id);

    // 构建指令数据
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
    swap_args.serialize(&mut instruction_data).unwrap();
    order_id.serialize(&mut instruction_data).unwrap();

    // 构建指令账户 - 基于 Orca 适配器的账户顺序
    let accounts = vec![
        AccountMeta::new(payer.pubkey(), true),        // user (signer, writable)
        AccountMeta::new_readonly(config_pda, false), // config (readable)
        AccountMeta::new(wsol_account, false),        // source_token_account (writable)
        AccountMeta::new(usdc_account, false),        // destination_token_account (writable)
        AccountMeta::new_readonly(wsol_mint, false),  // source_mint (readable)
        AccountMeta::new_readonly(usdc_mint, false),  // destination_mint (readable)


        // Orca Whirlpool 特定账户
        AccountMeta::new(pubkey!("whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc"), false), // dex_program_id
        AccountMeta::new_readonly(payer.pubkey(), true),
        AccountMeta::new(usdc_account, false),  // token_owner_account_b (usdc)
        AccountMeta::new(wsol_account, false), // token_owner_account_a (wsol)
        AccountMeta::new_readonly(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false), // token_program
        AccountMeta::new_readonly(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false), // token_program
        AccountMeta::new_readonly(pubkey!("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"), false), // memo

        AccountMeta::new(pubkey!("Czfq3xZZDmsdGdUyrNLtRhGc47cXcZtLG4crryfu44zE"), false),     // whirlpool
        AccountMeta::new(wsol_mint, false),     // mint a
        AccountMeta::new(usdc_mint, false),     // mint b
        AccountMeta::new(pubkey!("EUuUbDcafPrmVTD5M6qoJAoyyNbihBhugADAxRMn5he9"), false),     // token_vault_a
        AccountMeta::new(pubkey!("2WLWEuKDgkDUccTpbwYp1GToYktiSB1cXvreHUwiSUVP"), false),     // token_vault_b
        AccountMeta::new(pubkey!("EpmYr9EDCdiZgPmdfTeEupXMyPoBKbwkPrutSPUJLua"), false),      // tick_array0
        AccountMeta::new(pubkey!("38d2DowiQEn1BUxqHWt38yp4pZHjDzU87hynZ7dLnmYJ"), false),     // tick_array1
        AccountMeta::new(pubkey!("38d2DowiQEn1BUxqHWt38yp4pZHjDzU87hynZ7dLnmYJ"), false),     // tick_array2
        AccountMeta::new(pubkey!("FoKYKtRpD25TKzBMndysKpgPqbj8AdLXjfpYHXn9PGTX"), false), // oracle
        AccountMeta::new_readonly(pubkey!("HiDHyvKAa33shG6jrdNEYmi8WCKmwhW47TzSK4jL7Ugj"), false), // token_authority (signer)

    ];

    // 构建指令
    let instruction = Instruction {
        program_id,
        accounts,
        data: instruction_data,
    };

    // 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash().unwrap();

    // 构建并签名交易
    let message = Message::new(&[instruction], Some(&payer.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&payer], recent_blockhash);

    // 发送交易 (取消注释以实际执行)
    println!("\n构建交易成功!");
    println!("交易大小: {} 字节", transaction.message_data().len());
    println!("Orca Whirlpool 交换配置完成！");

    // 如需实际发送交易，取消注释以下代码:
    let signature = client.send_and_confirm_transaction(&transaction).unwrap();
    println!("交易签名: {}", signature);
    println!("Orca Whirlpool 交换操作成功！");
}


pub fn router_create_token_account() {
    let rpc_client = get_rpc();
    let for_address = pubkey!("2Aw1uS9o3CV2qurdysR9vLR9qtoWzL5xwX7oJcnrY7TA");
    let mint_address = pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
    // let mint_address = pubkey!("So11111111111111111111111111111111111111112");

    let acc = [98, 203, 88, 128, 173, 80, 204, 75, 101, 56, 11, 205, 180, 29, 51, 58, 96, 29, 98, 215, 244, 11, 64, 105, 93, 43, 246, 137, 179, 251, 40, 215, 17, 102, 253, 101, 70, 250, 25, 188, 108, 109, 57, 151, 174, 56, 190, 181, 250, 179, 43, 3, 110, 218, 243, 70, 27, 95, 205, 20, 161, 89, 92, 13];
    // 把 上边的 acc 转换成 Keypair
    let authority = Keypair::from_bytes(&acc).unwrap();


    let create_token_account_ix = create_associated_token_account_idempotent(
        &authority.pubkey(),
        &for_address,
        &mint_address,
        &spl_token::id(),
    );

    let mut tran = Transaction::new_with_payer(&[create_token_account_ix], Some(&authority.pubkey()));
    tran.sign(&[&authority], rpc_client.get_latest_blockhash().unwrap());
    match rpc_client.send_and_confirm_transaction(&tran) {
        Ok(signature) => {
            println!("Transaction successful with signature: {}", signature);
        }
        Err(e) => {
            println!("Transaction failed: {:?}", e);
        }
    }
}


// ALT 集成测试函数
pub async fn test_alt_integration() -> Result<(), Box<dyn std::error::Error>> {
    use crate::solana::alt_manager::{AltManager, AltError, DexType};

    println!("开始 ALT 集成测试...");

    // 1. 初始化 ALT 管理器
    let rpc_url = "https://www.techxk.com";
    let payer = get_test_keypair()?;

    let mut alt_manager = AltManager::recommended_setup(rpc_url, payer).await
        .map_err(|e| format!("ALT 管理器初始化失败: {:?}", e))?;

    // 2. 显示初始状态
    alt_manager.display_status().await;

    // 3. 添加各个 DEX 的地址
    println!("\n添加 DEX 地址到 ALT...");
    alt_manager.add_dex_addresses(DexType::Raydium).await?;
    alt_manager.add_dex_addresses(DexType::Meteora).await?;
    alt_manager.add_dex_addresses(DexType::PumpFun).await?;
    alt_manager.add_dex_addresses(DexType::Orca).await?;

    // 4. 显示更新后的状态
    alt_manager.display_status().await;

    // 5. 测试交易准备功能
    let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
    let usdc_mint = Pubkey::from_str(USDC_MINT)?;
    let wsol_account = Pubkey::from_str(WSOL_ACCOUNT)?;
    let usdc_account = Pubkey::from_str(USDC_ACCOUNT)?;

    let test_addresses = vec![
        wsol_mint,
        usdc_mint,
        wsol_account,
        usdc_account,
        Pubkey::from_str("3h2e43PunVA5K34vwKCLHWhZF4aZpyaC9RmxvshGAQpL")?, // Raydium 池
        Pubkey::from_str("HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR")?, // Meteora 池
    ];

    println!("\n准备交易地址索引...");
    let indices = alt_manager.prepare_for_transaction(test_addresses.clone()).await?;

    println!("地址索引映射:");
    for (addr, index) in test_addresses.iter().zip(indices.iter()) {
        println!("  {} -> 索引 {}", addr, index);
    }

    // 6. 保存状态到文件
    alt_manager.save_to_file("alt_test_backup.json")?;
    println!("\nALT 状态已保存到文件");

    // 7. 测试数据压缩效果
    println!("\n=== 数据压缩效果对比 ===");
    let original_size = test_addresses.len() * 32; // 每个地址 32 字节
    let compressed_size = indices.len() * 1; // 每个索引 1 字节
    let compression_ratio = (1.0 - compressed_size as f64 / original_size as f64) * 100.0;

    println!("原始地址数据: {} 字节", original_size);
    println!("压缩索引数据: {} 字节", compressed_size);
    println!("压缩率: {:.1}%", compression_ratio);

    println!("\nALT 集成测试完成！");
    Ok(())
}

pub fn router_wrap_sol() {
    let client = get_rpc();

    // 2. 加载付款人密钥对 (在实际使用中从文件加载)
    let acc = [98, 203, 88, 128, 173, 80, 204, 75, 101, 56, 11, 205, 180, 29, 51, 58, 96, 29, 98, 215, 244, 11, 64, 105, 93, 43, 246, 137, 179, 251, 40, 215, 17, 102, 253, 101, 70, 250, 25, 188, 108, 109, 57, 151, 174, 56, 190, 181, 250, 179, 43, 3, 110, 218, 243, 70, 27, 95, 205, 20, 161, 89, 92, 13];
    // 把 上边的 acc 转换成 Keypair
    let payer = Keypair::from_bytes(&acc).unwrap();

    let ata_address = get_associated_token_address(&payer.pubkey(), &spl_token::native_mint::id());
    /* Wrapped SOL's decimals is 9, hence amount to wrap is 1 SOL */
    let amount = 10 * 10_u64.pow(9);

    // create token account for wrapped sol
    // let create_ata_ix = create_associated_token_account_idempotent(
    //     &payer.pubkey(),
    //     &payer.pubkey(),
    //     &NATIVE_MINT_ID,
    //     &TOKEN_PROGRAM_ID,
    // );

    let program = pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
    let transfer_ix = transfer(&payer.pubkey(), &ata_address, amount);
    let sync_native_ix = sync_native(&program, &ata_address).unwrap();

    let mut transaction = Transaction::new_with_payer(
        &[transfer_ix, sync_native_ix],
        Some(&payer.pubkey()),
    );

    transaction.sign(&[&payer], client.get_latest_blockhash().unwrap());

    match client.send_and_confirm_transaction(&transaction) {
        Ok(signature) => println!("Transaction Signature: {}", signature),
        Err(err) => eprintln!("Error sending transaction: {}", err),
    }
}

// 多DEX组合线性路由测试：Raydium -> Meteora
pub fn router_multi_dex_linear_swap() {
    let client = get_test_client();
    let payer = get_test_keypair().unwrap();
    println!("付款人公钥: {}", payer.pubkey());

    let program_id = Pubkey::from_str(PROGRAM_ID).unwrap();
    let wsol_mint = Pubkey::from_str(WSOL_MINT).unwrap();
    let usdc_mint = Pubkey::from_str(USDC_MINT).unwrap();
    let wsol_account = Pubkey::from_str(WSOL_ACCOUNT).unwrap();
    let usdc_account = Pubkey::from_str(USDC_ACCOUNT).unwrap();

    let (config_pda, _config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );
    println!("配置账户 PDA: {}", config_pda);

    // 构建多DEX线性路由配置
    // 第一步：Raydium CLMM - WSOL -> USDC
    let route1 = Route {
        dex_id: 0,  // RaydiumClmm
        input_mint: wsol_mint,
        output_mint: usdc_mint,
    };

    // 第二步：Meteora DLMM - USDC -> WSOL
    let route2 = Route {
        dex_id: 2,  // MeteoraDlmm
        input_mint: usdc_mint,
        output_mint: wsol_mint,
    };

    let route_config = RouteConfig {
        routing_mode_id: 0,  // Linear - 线性模式
        routes: vec![route1, route2],  // 两个路由步骤
        amount_in: 50_000_000, // 0.05 SOL 作为初始输入
        min_amount_out: 1, // 预期最少得到的最终输出量
        max_slippage_bps: 500, // 5% 滑点（多步骤需要更高滑点容忍度）
        flash_loan: None, // 不使用闪电贷
    };

    // 构建 SwapArgs
    let swap_args = SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 1, // 预期最终输出量
        min_return: route_config.min_amount_out,
    };

    let order_id: u64 = 99999; // 多DEX组合测试订单ID

    println!("多DEX线性路由交换配置:");
    println!("  路由步骤: {} 步", route_config.routes.len());
    println!("  第1步: Raydium CLMM (WSOL -> USDC)");
    println!("  第2步: Meteora DLMM (USDC -> WSOL)");
    println!("  初始输入: {} WSOL (约 {} SOL)", swap_args.amount_in, swap_args.amount_in as f64 / 1_000_000_000.0);
    println!("  期望最终输出: {} WSOL", swap_args.expect_amount_out);
    println!("  最小返回: {} WSOL", swap_args.min_return);
    println!("  最大滑点: {} 基点 ({}%)", route_config.max_slippage_bps, route_config.max_slippage_bps as f64 / 100.0);
    println!("  订单 ID: {}", order_id);

    // 构建指令数据
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
    swap_args.serialize(&mut instruction_data).unwrap();
    order_id.serialize(&mut instruction_data).unwrap();

    // 构建账户列表 - 需要包含所有DEX的账户
    let mut accounts = vec![
        AccountMeta::new(payer.pubkey(), true),        // user (signer, writable)
        AccountMeta::new_readonly(config_pda, false), // config (readable)
        AccountMeta::new(wsol_account, false),        // source_token_account (writable)
        AccountMeta::new(wsol_account, false),        // destination_token_account (writable) - 最终输出也是WSOL
        AccountMeta::new_readonly(wsol_mint, false),  // source_mint (readable)
        AccountMeta::new_readonly(wsol_mint, false),  // destination_mint (readable) - 最终输出mint
    ];

    // Raydium CLMM 相关账户
    accounts.extend_from_slice(&[
        AccountMeta::new(pubkey!("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK"), false),
        AccountMeta::new(payer.pubkey(), false),
        AccountMeta::new(wsol_account, false),
        AccountMeta::new(usdc_account, false),

        AccountMeta::new(pubkey!("3h2e43PunVA5K34vwKCLHWhZF4aZpyaC9RmxvshGAQpL"), false),
        AccountMeta::new(pubkey!("3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv"), false),
        AccountMeta::new(pubkey!("4ct7br2vTPzfdmY3S5HLtTxcGSBfn6pnw98hsS6v359A"), false),
        AccountMeta::new(pubkey!("5it83u57VRrVgc51oNV19TTmAJuffPx5GtGwQr7gQNUo"), false),
        AccountMeta::new(pubkey!("3Y695CuQ8AP4anbwAqiEBeQF9KxqHFr8piEwvw3UePnQ"), false),
        AccountMeta::new(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false),
        AccountMeta::new(pubkey!("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"), false),
        AccountMeta::new(pubkey!("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"), false),
        AccountMeta::new(pubkey!("So11111111111111111111111111111111111111112"), false),
        AccountMeta::new(pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), false),
        AccountMeta::new(pubkey!("4NFvUKqknMpoe6CWTzK758B8ojVLzURL5pC6MtiaJ8TQ"), false),
        AccountMeta::new(pubkey!("3VuuojnJGqwFrPjwfbTD3GLKiiijGvktT9TUubW728W1"), false),
        AccountMeta::new(pubkey!("9KcgZN8TNMv5B873SfKZfrbtkaKzhRaZPZUg1UrveS36"), false),
        AccountMeta::new(pubkey!("3pZjqGBxqUF48bCVyFvbzbWGWtr31ATkFHYL9cMJLVoG"), false),
    ]);

    // Meteora DLMM 相关账户
    accounts.extend_from_slice(&[
        AccountMeta::new(pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"), false), // dex_program_id (meteora dlmm program)
        AccountMeta::new_readonly(payer.pubkey(), true),  // swap_authority_pubkey (signer)
        AccountMeta::new(wsol_account, false),            // swap_destination_token (第二步的输出)
        AccountMeta::new(usdc_account, false),            // swap_source_token (第二步的输入)

        AccountMeta::new(pubkey!("HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR"), false), // lb_pair
        AccountMeta::new_readonly(pubkey!("9HcJeBEsq5px2bYZbdo7vzQWVsPK3SHTkchy42hBn7HC"), false), // bin_array_bitmap_extension
        AccountMeta::new(pubkey!("H7j5NPopj3tQvDg4N8CxwtYciTn3e8AEV6wSVrxpyDUc"), false), // reserve_x
        AccountMeta::new(pubkey!("HbYjRzx7teCxqW3unpXBEcNHhfVZvW2vW9MQ99TkizWt"), false), // reserve_y
        AccountMeta::new_readonly(wsol_mint, false),      // token_x_mint
        AccountMeta::new_readonly(usdc_mint, false),      // token_y_mint
        AccountMeta::new(pubkey!("EgEYXef2FCoEYLHJJW74dMbom1atLXo6KwPuA6mSATYA"), false), // oracle
        AccountMeta::new(pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"), false), // host_fee_in
        AccountMeta::new_readonly(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false), // token_x_program
        AccountMeta::new_readonly(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false), // token_y_program
        AccountMeta::new_readonly(pubkey!("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"), false), // memo
        AccountMeta::new_readonly(pubkey!("D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6"), false), // event_authority
        AccountMeta::new(pubkey!("5Lw6FTwQAbfUns5iNfCpJFqBZjp8iQAMyRDwTqJQadSj"), false), // bin_array0
        AccountMeta::new(pubkey!("4tGcjYn8sf4RZ32uMPk2wiC9PzfrpubZZNephtnt9n5A"), false), // bin_array1
        AccountMeta::new(pubkey!("E3PAv7PgjMpX2TwAPHKuLFvMHi9t18dQ95Sva1XcUhp3"), false), // bin_array2
    ]);

    // 构建指令
    let instruction = Instruction {
        program_id,
        accounts,
        data: instruction_data,
    };

    // 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash().unwrap();

    // 构建并签名交易
    let message = Message::new(&[instruction], Some(&payer.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&payer], recent_blockhash);

    // 发送交易
    println!("\n发送多DEX线性路由交易...");
    let signature = client.send_and_confirm_transaction(&transaction).unwrap();
    println!("交易签名: {}", signature);
    println!("多DEX线性路由交换操作成功！");
    println!("完成路径: WSOL -> (Raydium) -> USDC -> (Meteora) -> WSOL");
}

// 多DEX组合线性路由测试：Raydium -> Pump
pub fn router_raydium_to_pump_linear_swap() {
    let client = get_test_client();
    let payer = get_test_keypair().unwrap();
    println!("付款人公钥: {}", payer.pubkey());

    let program_id = Pubkey::from_str(PROGRAM_ID).unwrap();
    let wsol_mint = Pubkey::from_str(WSOL_MINT).unwrap();
    let usdc_mint = Pubkey::from_str(USDC_MINT).unwrap();
    let wsol_account = Pubkey::from_str(WSOL_ACCOUNT).unwrap();
    let usdc_account = get_associated_token_address_with_program_id(
        &payer.pubkey(),
        &usdc_mint,
        &spl_token::id(),
    );

    let (config_pda, _config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );
    println!("配置账户 PDA: {}", config_pda);

    // 获取用户体积累积器 PDA
    let user_volume = get_user_volume_pda(&payer.pubkey());

    // 构建多DEX线性路由配置
    // 第一步：Raydium CLMM - WSOL -> USDC
    let route1 = Route {
        dex_id: 0,  // RaydiumClmm
        input_mint: wsol_mint,
        output_mint: usdc_mint,
    };

    // 第二步：Pump SwapSell - USDC -> WSOL
    let route2 = Route {
        dex_id: 6,  // PumpSwapSell
        input_mint: usdc_mint,
        output_mint: wsol_mint,
    };

    let route_config = RouteConfig {
        routing_mode_id: 0,  // Linear - 线性模式
        routes: vec![route1, route2],  // 两个路由步骤
        amount_in: 80_000_000, // 0.08 SOL 作为初始输入
        min_amount_out: 1, // 预期最少得到的最终输出量
        max_slippage_bps: 600, // 6% 滑点（包含Pump的费用和滑点）
        flash_loan: None, // 不使用闪电贷
    };

    // 构建 SwapArgs
    let swap_args = SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 1, // 预期最终输出量
        min_return: route_config.min_amount_out,
    };

    let order_id: u64 = 88888; // Raydium到Pump组合测试订单ID

    println!("Raydium -> Pump 线性路由交换配置:");
    println!("  路由步骤: {} 步", route_config.routes.len());
    println!("  第1步: Raydium CLMM (WSOL -> USDC)");
    println!("  第2步: Pump SwapSell (USDC -> WSOL)");
    println!("  初始输入: {} WSOL (约 {} SOL)", swap_args.amount_in, swap_args.amount_in as f64 / 1_000_000_000.0);
    println!("  期望最终输出: {} WSOL", swap_args.expect_amount_out);
    println!("  最小返回: {} WSOL", swap_args.min_return);
    println!("  最大滑点: {} 基点 ({}%)", route_config.max_slippage_bps, route_config.max_slippage_bps as f64 / 100.0);
    println!("  订单 ID: {}", order_id);

    // 构建指令数据
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
    swap_args.serialize(&mut instruction_data).unwrap();
    order_id.serialize(&mut instruction_data).unwrap();

    // 构建账户列表 - 需要包含所有DEX的账户
    let mut accounts = vec![
        AccountMeta::new(payer.pubkey(), true),        // user (signer, writable)
        AccountMeta::new_readonly(config_pda, false), // config (readable)
        AccountMeta::new(wsol_account, false),        // source_token_account (writable)
        AccountMeta::new(wsol_account, false),        // destination_token_account (writable) - 最终输出也是WSOL
        AccountMeta::new_readonly(wsol_mint, false),  // source_mint (readable)
        AccountMeta::new_readonly(wsol_mint, false),  // destination_mint (readable) - 最终输出mint
    ];

    // Raydium CLMM 相关账户
    accounts.extend_from_slice(&[
        AccountMeta::new(pubkey!("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK"), false),
        AccountMeta::new(payer.pubkey(), false),
        AccountMeta::new(wsol_account, false),
        AccountMeta::new(usdc_account, false),

        AccountMeta::new(pubkey!("3h2e43PunVA5K34vwKCLHWhZF4aZpyaC9RmxvshGAQpL"), false),
        AccountMeta::new(pubkey!("3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv"), false),
        AccountMeta::new(pubkey!("4ct7br2vTPzfdmY3S5HLtTxcGSBfn6pnw98hsS6v359A"), false),
        AccountMeta::new(pubkey!("5it83u57VRrVgc51oNV19TTmAJuffPx5GtGwQr7gQNUo"), false),
        AccountMeta::new(pubkey!("3Y695CuQ8AP4anbwAqiEBeQF9KxqHFr8piEwvw3UePnQ"), false),
        AccountMeta::new(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false),
        AccountMeta::new(pubkey!("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"), false),
        AccountMeta::new(pubkey!("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"), false),
        AccountMeta::new(pubkey!("So11111111111111111111111111111111111111112"), false),
        AccountMeta::new(pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), false),
        AccountMeta::new(pubkey!("4NFvUKqknMpoe6CWTzK758B8ojVLzURL5pC6MtiaJ8TQ"), false),
        AccountMeta::new(pubkey!("3VuuojnJGqwFrPjwfbTD3GLKiiijGvktT9TUubW728W1"), false),
        AccountMeta::new(pubkey!("9KcgZN8TNMv5B873SfKZfrbtkaKzhRaZPZUg1UrveS36"), false),
        AccountMeta::new(pubkey!("3pZjqGBxqUF48bCVyFvbzbWGWtr31ATkFHYL9cMJLVoG"), false),
    ]);

    // Pump.fun 相关账户
    accounts.extend_from_slice(&[
        AccountMeta::new(pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA"), false), // pump program
        AccountMeta::new(payer.pubkey(), false),
        AccountMeta::new(usdc_account, false),        // 第二步的输入账户
        AccountMeta::new(wsol_account, false),        // 第二步的输出账户

        AccountMeta::new(pubkey!("Gf7sXMoP8iRw4iiXmJ1nq4vxcRycbGXy5RL8a8LnTd3v"), false),
        AccountMeta::new(pubkey!("ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw"), false),
        AccountMeta::new(pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), false),
        AccountMeta::new(pubkey!("So11111111111111111111111111111111111111112"), false),
        AccountMeta::new(pubkey!("nML7msD1MiJHxFvhv4po1u6C4KpWr64ugKqc75DMuD2"), false),
        AccountMeta::new(pubkey!("EjHirXt2bQd2DDNveagHHCWYzUwtY1iwNbBrV5j84e6j"), false),
        AccountMeta::new(pubkey!("AVmoTthdrX6tKt4nDjco2D775W2YK3sDhxPcMmzUAmTY"), false),
        AccountMeta::new(pubkey!("FGptqdxjahafaCzpZ1T6EDtCzYMv7Dyn5MgBLyB3VUFW"), false),
        AccountMeta::new(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false),
        AccountMeta::new(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false),
        AccountMeta::new(pubkey!("11111111111111111111111111111111"), false),
        AccountMeta::new(pubkey!("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"), false),
        AccountMeta::new(pubkey!("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR"), false),
        AccountMeta::new(pubkey!("Ei6iux5MMYG8JxCTr58goADqFTtMroL9TXJityF3fAQc"), false),
        AccountMeta::new(pubkey!("8N3GDaZ2iwN65oxVatKTLPNooAVUJTbfiVJ1ahyqwjSk"), false),
    ]);

    // 构建指令
    let instruction = Instruction {
        program_id,
        accounts,
        data: instruction_data,
    };

    // 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash().unwrap();

    // 构建并签名交易
    let message = Message::new(&[instruction], Some(&payer.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&payer], recent_blockhash);

    // 发送交易
    println!("\n发送 Raydium -> Pump 线性路由交易...");
    let signature = client.send_and_confirm_transaction(&transaction).unwrap();
    println!("交易签名: {}", signature);
    println!("Raydium -> Pump 线性路由交换操作成功！");
    println!("完成路径: WSOL -> (Raydium CLMM) -> USDC -> (Pump SwapSell) -> WSOL");
}

// Meteora DLMM 交易测试
pub fn router_meteora_dlmm_swap() {
    let client = get_test_client();
    let payer = get_test_keypair().unwrap();
    println!("付款人公钥: {}", payer.pubkey());

    let program_id = Pubkey::from_str(PROGRAM_ID).unwrap();
    let wsol_mint = Pubkey::from_str(WSOL_MINT).unwrap();
    let usdc_mint = Pubkey::from_str(USDC_MINT).unwrap();
    let wsol_account = Pubkey::from_str(WSOL_ACCOUNT).unwrap();
    let usdc_account = Pubkey::from_str(USDC_ACCOUNT).unwrap();

    let (config_pda, _config_bump) = Pubkey::find_program_address(
        &[CONFIG_SEED],
        &program_id,
    );
    println!("配置账户 PDA: {}", config_pda);

    // 构建路由配置
    let route = Route {
        dex_id: 2,  // MeteoraDlmm
        input_mint: wsol_mint,
        output_mint: usdc_mint,
    };

    let route_config = RouteConfig {
        routing_mode_id: 0,  // Linear
        routes: vec![route],
        amount_in: 11_000_000, // 0.1 SOL
        min_amount_out: 1, // 预期最少得到的 USDC 数量
        max_slippage_bps: 300, // 3% 滑点
        flash_loan: None, // 不使用闪电贷
    };

    // 构建 SwapArgs
    let swap_args = SwapArgs {
        route_config: route_config.clone(),
        amount_in: route_config.amount_in,
        expect_amount_out: 1, // 预期输出量
        min_return: route_config.min_amount_out,
    };

    let order_id: u64 = 12347;

    println!("Meteora DLMM 交换配置:");
    println!("  输入代币: {} (WSOL)", wsol_mint);
    println!("  输出代币: {} (USDC)", usdc_mint);
    println!("  输入金额: {} (约 {} SOL)", swap_args.amount_in, swap_args.amount_in as f64 / 1_000_000_000.0);
    println!("  期望输出: {}", swap_args.expect_amount_out);
    println!("  最小返回: {}", swap_args.min_return);
    println!("  最大滑点: {} 基点 ({}%)", route_config.max_slippage_bps, route_config.max_slippage_bps as f64 / 100.0);
    println!("  订单 ID: {}", order_id);

    // 构建指令数据
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&SWAP_DISCRIMINATOR);
    swap_args.serialize(&mut instruction_data).unwrap();
    order_id.serialize(&mut instruction_data).unwrap();

    // 根据 meteora.rs 适配器的账户顺序构建指令账户
    let accounts = vec![
        AccountMeta::new(payer.pubkey(), true),        // user (signer, writable)
        AccountMeta::new_readonly(config_pda, false), // config (readable)
        AccountMeta::new(wsol_account, false),        // source_token_account (writable)
        AccountMeta::new(usdc_account, false),        // destination_token_account (writable)
        AccountMeta::new_readonly(wsol_mint, false),  // source_mint (readable)
        AccountMeta::new_readonly(usdc_mint, false),  // destination_mint (readable)

        // Meteora DLMM 特定账户（基于你提供的地址）
        AccountMeta::new(pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"), false), // dex_program_id (meteora dlmm program)
        AccountMeta::new_readonly(payer.pubkey(), true),  // swap_authority_pubkey (signer)
        AccountMeta::new(usdc_account, false),            // swap_destination_token
        AccountMeta::new(wsol_account, false),            // swap_source_token

        AccountMeta::new(pubkey!("HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR"), false), // lb_pair
        AccountMeta::new_readonly(pubkey!("9HcJeBEsq5px2bYZbdo7vzQWVsPK3SHTkchy42hBn7HC"), false), // bin_array_bitmap_extension
        AccountMeta::new(pubkey!("H7j5NPopj3tQvDg4N8CxwtYciTn3e8AEV6wSVrxpyDUc"), false), // reserve_x
        AccountMeta::new(pubkey!("HbYjRzx7teCxqW3unpXBEcNHhfVZvW2vW9MQ99TkizWt"), false), // reserve_y
        AccountMeta::new_readonly(wsol_mint, false),      // token_x_mint
        AccountMeta::new_readonly(usdc_mint, false),      // token_y_mint
        AccountMeta::new(pubkey!("EgEYXef2FCoEYLHJJW74dMbom1atLXo6KwPuA6mSATYA"), false), // oracle
        AccountMeta::new(pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"), false), // host_fee_in
        AccountMeta::new_readonly(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false), // token_x_program
        AccountMeta::new_readonly(pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), false), // token_y_program
        AccountMeta::new_readonly(pubkey!("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"), false), // memo
        AccountMeta::new_readonly(pubkey!("D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6"), false), // event_authority
        AccountMeta::new(pubkey!("5Lw6FTwQAbfUns5iNfCpJFqBZjp8iQAMyRDwTqJQadSj"), false), // bin_array0
        AccountMeta::new(pubkey!("4tGcjYn8sf4RZ32uMPk2wiC9PzfrpubZZNephtnt9n5A"), false), // bin_array1
        AccountMeta::new(pubkey!("E3PAv7PgjMpX2TwAPHKuLFvMHi9t18dQ95Sva1XcUhp3"), false), // bin_array2
    ];

    // 构建指令
    let instruction = Instruction {
        program_id,
        accounts,
        data: instruction_data,
    };

    // 获取最新的区块哈希
    let recent_blockhash = client.get_latest_blockhash().unwrap();

    // 构建并签名交易
    let message = Message::new(&[instruction], Some(&payer.pubkey()));
    let mut transaction = Transaction::new_unsigned(message);
    transaction.sign(&[&payer], recent_blockhash);

    // 发送交易 (取消注释以实际执行)
    println!("\n构建交易成功!");
    println!("交易大小: {} 字节", transaction.message_data().len());
    println!("Meteora DLMM 交换配置完成！");

    // 如需实际发送交易，取消注释以下代码:
    let signature = client.send_and_confirm_transaction(&transaction).unwrap();
    println!("交易签名: {}", signature);
    println!("Meteora DLMM 交换操作成功！");
}
