use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::mpsc;
use tracing::{debug, error, info, warn};
use markets::dex::jupiter;
use markets::dex::jupiter::model::{DerivedPrice, QuoteResponse};
use markets::dex::jupiter::model::QuoteResponse::{Jupiter, Plan};
use crate::{ActorMessage, SWAP_AMOUNT};

// Jupiter 价格轮询器
pub async fn jupiter_poller(actor_senders: Arc<HashMap<String, mpsc::UnboundedSender<ActorMessage>>>) {
    debug!("Jupiter Poller started for pairs");

    let usd = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";
    let sol = "So11111111111111111111111111111111111111112";
    let sonic = "SonicxvLud67EceaEzCLRnMTBqzYUUYNr93DBkBdDES";
    let jupiter = "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN";
    let fartcoin = "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump";

    let mut pairs = HashMap::new();
    // pairs.insert(sonic.to_string(), "SONICUSDT");
    // pairs.insert(jupiter.to_string(), "JUPUSDT");
    pairs.insert(fartcoin.to_string(), "FARTCOINUSDT");

    loop {
        let req = format!("{},{}, {}", sonic, jupiter, fartcoin);
        let prices: HashMap<String, DerivedPrice> = jupiter::price(req, usd.to_string())
            .await
            .expect("Failed to get price");
        for (address, price) in prices.iter() {

            for (pair_address, pair_symbol) in pairs.iter() {
                if address == pair_address {
                    // 发送给对应的 Actor
                    if let Some(sender) = actor_senders.get(*pair_symbol) {
                        if let Err(e) = sender.send(ActorMessage::JupiterUpdate(price.clone())) {
                            error!("Failed to send Jupiter price for {}: {}", address, e);
                        }
                    } else {
                        warn!("No actor found for Jupiter symbol: {}", address);
                    }
                }
            }
        }
        tokio::time::sleep(Duration::from_secs(5)).await;
    }
}


pub async fn jupiter_quote_poller(actor_senders: Arc<HashMap<String, mpsc::UnboundedSender<ActorMessage>>>) {
    debug!("Jupiter Quote Poller started for pairs");

    let sol = "So11111111111111111111111111111111111111112";
    let symbol_address = "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr";
    let symbol = "POPCATUSDT";

    loop {
        let quote = jupiter::quote(sol.to_string(), symbol_address.to_string(), SWAP_AMOUNT)
            .await;

        if let Err(e) = quote {
            error!("Failed to get Jupiter quote: {}", e);
            tokio::time::sleep(Duration::from_secs(30)).await;
            continue;
        }

        match quote.unwrap() {
            Jupiter(q) => {
                let num = q.order_info.output.start_amount.parse::<f64>().unwrap_or(0.0);
                info!("Jupiter quote num: {}", num);
                let address = q.order_info.output.token;
                if num > 0.0 {
                    let price = num / SWAP_AMOUNT as f64;
                    // 发送给对应的 Actor
                    // if let Some(sender) = actor_senders.get(*pair_symbol) {
                    //     if let Err(e) = sender.send(
                    //         ActorMessage::JupiterUpdate(
                    //             DerivedPrice::new(num.to_string(), price.to_string(), num.to_string())
                    //         )) {
                    //         error!("Failed to send Jupiter price for {}: {}", address, e);
                    //     }
                    // } else {}
                    //
                }
            }
            Plan(q) => {
                // 发送给对应的 Actor
                if let Some(sender) = actor_senders.get(symbol) {
                    if let Err(e) =
                        sender.send(ActorMessage::JupiterQuoteUpdate(q)) {
                        error!("Failed to send Jupiter price for {}: {}", symbol, e);
                    }
                } else {
                    warn!("No actor found for Jupiter symbol: {}", symbol);
                }
            }
        }

        tokio::time::sleep(Duration::from_secs(1)).await;
    }
}
