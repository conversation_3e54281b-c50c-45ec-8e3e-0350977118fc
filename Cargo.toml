[package]
name = "arbitide"
version = "0.1.0"
edition = "2024"
resolver = "2"


[workspace]
members = ["app", "chains", "common", "execution-engine", "markets", "notify", "risk-engine", "strategies"]

[workspace.dependencies]
anyhow = "1.0.97"
bytes = "1.10.1"
chrono = "0.4.40"
hex = "0.4.3"
hmac = "0.12.1"
reqwest = { version = "0.12.15", features = ["json", "rustls-tls"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
sha2 = "0.10.8"
sqlx = { version = "0.8.3", features = ["runtime-tokio-rustls", "postgres", "macros", "chrono", "uuid"] }
thiserror = "2.0.12"
tokio = { version = "1.44.2", features = ["rt", "rt-multi-thread", "macros", "sync"] }
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-subscriber = "0.3.19"
url = "2.5.4"
uuid = { version = "1.16.0", features = ["v4"] }
tungstenite = { version = "0.26.2", features = ["rustls-tls-native-roots"] }
tokio-tungstenite = { version = "0.26.2", features = ["rustls-tls-native-roots"] }
rustls = { version = "0.23.25", features = ["ring"] }
mimalloc = "0.1.46"
rand = "0.9.0"
futures = "0.3.31"
once_cell = "1.21.3"
solana-sdk = "2.2.2"
solana-client = "2.2.7"
base64 = "0.22.1"
bincode = { version = "2.0.1", features = ["serde"] }


[dependencies]
tokio.workspace = true
rustls.workspace = true
mimalloc.workspace = true

app = { path = "app" }
