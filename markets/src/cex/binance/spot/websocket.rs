use std::net::TcpStream;
use std::sync::atomic::{AtomicBool, Ordering};
use bytes::Bytes;
use serde::{Deserialize, Serialize};
use tungstenite::{Message, WebSocket};
use tungstenite::stream::MaybeTlsStream;
use tungstenite::handshake::client::Response;
use url::Url;
use crate::cex::binance::spot::model::{BookTicker, DepthOrderBookEvent, KlineEvent, OrderBookInfo, TradeEvent};

#[allow(clippy::all)]
enum WebsocketAPI {
    Default,
    MultiStream,
    Custom(String)
}

impl WebsocketAPI {
    fn params(self, subscription: &str) -> String {
        match self {
            WebsocketAPI::Default => format!("wss://data-stream.binance.vision:443/ws/{}", subscription),
            WebsocketAPI::MultiStream => format!(
                "wss://data-stream.binance.vision:9443/stream?streams={}",
                subscription
            ),
            WebsocketAPI::Custom(url) => format!("{}/{}", url, subscription),
        }
    }
}


#[derive(Serialize, Deserialize, Debug)]
#[serde(untagged)]
pub enum WebsocketEvent {
    Kline(KlineEvent),
    Trade(TradeEvent),
    DepthOrderBook(DepthOrderBookEvent),
    OrderBook(OrderBookInfo),
    BookTickerEvent(BookTicker),
}




pub struct WebSockets<'a> {
    pub socket: Option<(WebSocket<MaybeTlsStream<TcpStream>>, Response)>,
    handler: Box<dyn FnMut(WebsocketEvent) -> Result<(), String> + 'a>
}

impl<'a> WebSockets<'a> {
    pub fn new<Callback>(handler: Callback) -> Self
    where Callback: FnMut(WebsocketEvent) -> Result<(), String> + 'a
    {
        Self {
            socket: None,
            handler: Box::new(handler)
        }
    }


    pub fn connect(&mut self, subscription: &str) -> Result<(), String> {
        self.connect_wss(&WebsocketAPI::Default.params(subscription))
    }

    pub fn connect_multiple_streams(&mut self, endpoints: &[String]) -> Result<(), String> {
        self.connect_wss(&WebsocketAPI::MultiStream.params(&endpoints.join("/")))
    }

    fn connect_wss(&mut self, wss: &str) -> Result<(), String> {
        let url = Url::parse(wss).map_err(|e1| {
            format!("Error parsing url: {}", e1)
        })?;
        // match tungstenite::connect(url) {
        //     Ok(answer) => {
        //         self.socket = Some(answer);
        //         Ok(())
        //     }
        //     // Err(e) => Err(BNError::DuringHandshake(e.to_string()).into()),
        //     Err(e) => Err(e.to_string())
        // }

        Ok(())
    }

    pub fn disconnect(&mut self) -> Result<(), String> {
        if let Some(ref mut socket) = self.socket {
            socket.0.close(None).map_err(|e| {
                format!("Error closing the connection: {}", e)
            })?;
            return Ok(());
        }
        // Err(BNError::NotAbleConnect.into())
        Err("Not able to connect".to_string())
    }

    pub fn handle_msg(&mut self, msg: &str) -> Result<(), String> {
        let value: serde_json::Value = serde_json::from_str(msg).map_err(|e| {
            format!("Error parsing message: {}", e)
        })?;
        if let Some(data) = value.get("data") {
            self.handle_msg(&data.to_string())?;
            return Ok(());
        }

        tracing::info!("event data value: {}", value);

        if let Ok(events) = serde_json::from_value::<WebsocketEvent>(value){
            let action = match events {
                WebsocketEvent::Kline(v) => WebsocketEvent::Kline(v),
                WebsocketEvent::Trade(v) => WebsocketEvent::Trade(v),
                WebsocketEvent::OrderBook(v) => WebsocketEvent::OrderBook(v),
                WebsocketEvent::BookTickerEvent(v) => WebsocketEvent::BookTickerEvent(v),
                WebsocketEvent::DepthOrderBook(v) => WebsocketEvent::DepthOrderBook(v),
            };
            (self.handler)(action)?;
        }
        Ok(())
    }

    pub fn event_loop(&mut self, running: &AtomicBool) -> Result<(), String> {
        while running.load(Ordering::Relaxed) {
            if let Some(ref mut socket) = self.socket {
                let msg = socket.0.read().map_err(|e1| {
                    format!("Error reading message: {}", e1)
                })?;
                match msg {
                    Message::Text(text) => {
                        if let Err(e) = self.handle_msg(&text) {
                            println!("handle msg Error: {:?}", e);
                        }
                    }
                    Message::Ping(_) => {
                        socket.0.write(Message::Pong(Bytes::from(vec![]))).map_err(|e2| {
                            format!("Error writing message: {}", e2)
                        })?;
                    }
                    Message::Pong(_) | Message::Binary(_) | Message::Frame(_) => (),
                    Message::Close(_) => {
                        println!("{}", "Close message received");
                    }
                }
            }
        }
        Ok(())
    }
}
