use std::collections::HashMap;
use std::error::Error;
use std::fmt;
use std::str::FromStr;
use borsh::{BorshDeserialize, BorshSerialize};
use serde::{Deserialize, Serialize};
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::signature::{Keypair, Signer};
use solana_sdk::pubkey::Pubkey;
use solana_sdk::transaction::Transaction;
use solana_sdk::address_lookup_table::{
    instruction::{create_lookup_table, extend_lookup_table},
    state::AddressLookupTable,
};

#[derive(Debug)]
pub enum AltError {
    AltNotInitialized,
    AddressNotFound(Pubkey),
    SyncFailed(String),
    ChainOperationFailed(String),
    IndexOverflow,
    SerializationError(String),
}

impl fmt::Display for AltError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AltError::AltNotInitialized => write!(f, "ALT 账户未初始化"),
            AltError::AddressNotFound(addr) => write!(f, "地址未找到: {}", addr),
            AltError::SyncFailed(msg) => write!(f, "同步失败: {}", msg),
            AltError::ChainOperationFailed(msg) => write!(f, "链上操作失败: {}", msg),
            AltError::IndexOverflow => write!(f, "索引溢出，ALT 最大支持 256 个地址"),
            AltError::SerializationError(msg) => write!(f, "序列化错误: {}", msg),
        }
    }
}

impl Error for AltError {}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LocalAltRegistry {
    pub address_to_index: HashMap<String, u8>,
    pub index_to_address: HashMap<u8, String>,
    pub next_index: u8,
    pub alt_account: Option<String>,
}

impl LocalAltRegistry {
    pub fn new() -> Self {
        Self {
            address_to_index: HashMap::new(),
            index_to_address: HashMap::new(),
            next_index: 0,
            alt_account: None,
        }
    }

    pub fn add_address(&mut self, address: Pubkey) -> Result<u8, AltError> {
        let addr_str = address.to_string();

        if let Some(index) = self.address_to_index.get(&addr_str) {
            return Ok(*index);
        }

        if self.next_index >= 255 {
            return Err(AltError::IndexOverflow);
        }

        let index = self.next_index;
        self.address_to_index.insert(addr_str.clone(), index);
        self.index_to_address.insert(index, addr_str);
        self.next_index += 1;

        Ok(index)
    }

    pub fn get_address(&self, index: u8) -> Option<Pubkey> {
        self.index_to_address.get(&index)
            .and_then(|s| Pubkey::from_str(s).ok())
    }

    pub fn get_index(&self, address: &Pubkey) -> Option<u8> {
        let addr_str = address.to_string();
        self.address_to_index.get(&addr_str).copied()
    }

    pub fn contains_address(&self, address: &Pubkey) -> bool {
        let addr_str = address.to_string();
        self.address_to_index.contains_key(&addr_str)
    }

    pub fn len(&self) -> usize {
        self.address_to_index.len()
    }

    pub fn clear(&mut self) {
        self.address_to_index.clear();
        self.index_to_address.clear();
        self.next_index = 0;
    }
}

#[derive(Clone, Copy, Debug)]
pub enum DexType {
    Raydium,
    Meteora,
    PumpFun,
    Orca,
}

pub struct AltManager {
    pub rpc_client: RpcClient,
    pub payer: Keypair,
    pub local_registry: LocalAltRegistry,
    pub alt_account: Option<Pubkey>,
}

impl AltManager {
    pub fn new(rpc_url: &str, payer: Keypair) -> Self {
        let rpc_client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::processed());

        Self {
            rpc_client,
            payer,
            local_registry: LocalAltRegistry::new(),
            alt_account: None,
        }
    }

    pub async fn create_alt(&mut self) -> Result<Pubkey, AltError> {
        let recent_slot = self.rpc_client.get_slot()
            .map_err(|e| AltError::ChainOperationFailed(format!("获取 slot 失败: {}", e)))?;

        let (create_alt_ix, alt_address) = create_lookup_table(
            self.payer.pubkey(),
            self.payer.pubkey(),
            recent_slot,
        );

        let recent_blockhash = self.rpc_client.get_latest_blockhash()
            .map_err(|e| AltError::ChainOperationFailed(format!("获取区块哈希失败: {}", e)))?;

        let transaction = Transaction::new_signed_with_payer(
            &[create_alt_ix],
            Some(&self.payer.pubkey()),
            &[&self.payer],
            recent_blockhash,
        );

        let signature = self.rpc_client.send_and_confirm_transaction(&transaction)
            .map_err(|e| AltError::ChainOperationFailed(format!("发送创建 ALT 交易失败: {}", e)))?;

        println!("ALT 创建成功，地址: {}, 签名: {}", alt_address, signature);

        self.alt_account = Some(alt_address);
        self.local_registry.alt_account = Some(alt_address.to_string());

        Ok(alt_address)
    }

    pub fn get_common_addresses(&mut self) -> Vec<Pubkey> {
        let mut addresses = Vec::new();

        let system_addresses = vec![
            // 系统程序地址
            Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(), // SPL Token
            Pubkey::from_str("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb").unwrap(),  // Token-2022
            Pubkey::from_str("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr").unwrap(),   // Memo
            Pubkey::from_str("11111111111111111111111111111111").unwrap(),            // System
            Pubkey::from_str("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL").unwrap(),  // ATA
        ];

        let token_addresses = vec![
            // 常用代币地址
            Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),  // WSOL
            Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(), // USDC
        ];

        let dex_addresses = vec![
            // DEX 程序地址
            Pubkey::from_str("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK").unwrap(), // Raydium
            Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo").unwrap(),  // Meteora
            Pubkey::from_str("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA").unwrap(),  // Pump.fun
            Pubkey::from_str("whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc").unwrap(), // Orca
        ];

        // 添加到本地注册表并收集地址
        for addr in system_addresses.iter()
            .chain(token_addresses.iter())
            .chain(dex_addresses.iter()) {
            if let Ok(index) = self.local_registry.add_address(*addr) {
                addresses.push(*addr);
                println!("预设地址 {} -> 索引 {}", addr, index);
            }
        }

        addresses
    }

    pub fn get_address_index_safe(&self, address: &Pubkey) -> Result<u8, AltError> {
        self.local_registry.get_index(address)
            .ok_or(AltError::AddressNotFound(*address))
    }

    pub async fn extend_alt(&mut self, new_addresses: Vec<Pubkey>) -> Result<(), AltError> {
        let alt_address = self.alt_account.ok_or(AltError::AltNotInitialized)?;

        if new_addresses.is_empty() {
            return Ok(());
        }

        let extend_alt_ix = extend_lookup_table(
            alt_address,
            self.payer.pubkey(),
            Some(self.payer.pubkey()),
            new_addresses.clone(),
        );

        let recent_blockhash = self.rpc_client.get_latest_blockhash()
            .map_err(|e| AltError::ChainOperationFailed(format!("获取区块哈希失败: {}", e)))?;

        let transaction = Transaction::new_signed_with_payer(
            &[extend_alt_ix],
            Some(&self.payer.pubkey()),
            &[&self.payer],
            recent_blockhash,
        );

        let signature = self.rpc_client.send_and_confirm_transaction(&transaction)
            .map_err(|e| AltError::ChainOperationFailed(format!("扩展 ALT 交易失败: {}", e)))?;

        println!("ALT 扩展成功，添加 {} 个地址，签名: {}", new_addresses.len(), signature);

        // 更新本地副本
        for address in new_addresses {
            let _ = self.local_registry.add_address(address)?;
        }

        Ok(())
    }

    pub async fn initialize_with_common_addresses(&mut self) -> Result<(), AltError> {
        if self.alt_account.is_none() {
            self.create_alt().await?;
        }

        let common_addresses = self.get_common_addresses();

        // 分批添加地址（每批最多30个地址）
        const BATCH_SIZE: usize = 30;
        for chunk in common_addresses.chunks(BATCH_SIZE) {
            self.extend_alt(chunk.to_vec()).await?;
            // 等待确认后再继续下一批
            tokio::time::sleep(std::time::Duration::from_millis(500)).await;
        }

        println!("ALT 初始化完成，共添加 {} 个地址", common_addresses.len());
        Ok(())
    }

    pub async fn add_pool_addresses(&mut self, pool_addresses: Vec<Pubkey>) -> Result<(), AltError> {
        let mut new_addresses = Vec::new();

        // 检查哪些地址是新的
        for address in pool_addresses {
            if !self.local_registry.contains_address(&address) {
                new_addresses.push(address);
            }
        }

        if !new_addresses.is_empty() {
            let count = new_addresses.len();
            self.extend_alt(new_addresses).await?;
            println!("动态添加了 {} 个新的池子地址", count);
        }

        Ok(())
    }

    pub async fn add_dex_addresses(&mut self, dex_type: DexType) -> Result<(), AltError> {
        let addresses = match dex_type {
            DexType::Raydium => self.get_raydium_addresses(),
            DexType::Meteora => self.get_meteora_addresses(),
            DexType::PumpFun => self.get_pump_addresses(),
            DexType::Orca => self.get_orca_addresses(),
        };

        self.add_pool_addresses(addresses).await
    }

    pub async fn add_kamino_address(&mut self) -> Result<(), AltError> {
        let kms = self.get_kamino_addresses();
        self.add_pool_addresses(kms).await
    }

    pub fn get_raydium_addresses(&self) -> Vec<Pubkey> {
        vec![
            // Raydium 常用池子地址
            Pubkey::from_str("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK").unwrap(),
            Pubkey::from_str("2Aw1uS9o3CV2qurdysR9vLR9qtoWzL5xwX7oJcnrY7TA").unwrap(),
            Pubkey::from_str("8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG").unwrap(),
            Pubkey::from_str("5GFFRSdJXK5vnsNLjfqYJKgvNcTrVdcXMCFek6Cs4Jbb").unwrap(),
            Pubkey::from_str("3h2e43PunVA5K34vwKCLHWhZF4aZpyaC9RmxvshGAQpL").unwrap(),
            Pubkey::from_str("3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv").unwrap(),
            Pubkey::from_str("4ct7br2vTPzfdmY3S5HLtTxcGSBfn6pnw98hsS6v359A").unwrap(),
            Pubkey::from_str("5it83u57VRrVgc51oNV19TTmAJuffPx5GtGwQr7gQNUo").unwrap(),
            Pubkey::from_str("3Y695CuQ8AP4anbwAqiEBeQF9KxqHFr8piEwvw3UePnQ").unwrap(),
            Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(),
            Pubkey::from_str("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb").unwrap(),
            Pubkey::from_str("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr").unwrap(),
            Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),
            Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(),
            Pubkey::from_str("4NFvUKqknMpoe6CWTzK758B8ojVLzURL5pC6MtiaJ8TQ").unwrap(),
            Pubkey::from_str("3VuuojnJGqwFrPjwfbTD3GLKiiijGvktT9TUubW728W1").unwrap(),
            Pubkey::from_str("9KcgZN8TNMv5B873SfKZfrbtkaKzhRaZPZUg1UrveS36").unwrap(),
        ]
    }

    pub fn get_kamino_addresses(&self) -> Vec<Pubkey> {
        vec![
            Pubkey::from_str("KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD").unwrap(),
            Pubkey::from_str("2Aw1uS9o3CV2qurdysR9vLR9qtoWzL5xwX7oJcnrY7TA").unwrap(),
            Pubkey::from_str("Dx8iy2o46sK1DzWbEcznqSKeLbLVeu7otkibA3WohGAj").unwrap(),
            Pubkey::from_str("H6rHXmXoCQvq8Ue81MqNh7ow5ysPa1dSozwW3PU1dDH6").unwrap(),
            Pubkey::from_str("6gTJfuPHEg6uRAijRkMqNc9kan4sVZejKMxmvx2grT1p").unwrap(),
            Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),
            Pubkey::from_str("ywaaLvG7t1vXJo8sT3UzE8yzzZtxLM7Fmev64Jbooye").unwrap(),
            Pubkey::from_str("8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG").unwrap(),
            Pubkey::from_str("EQ7hw63aBS7aPQqXsoxaaBxiwbEzaAiY9Js6tCekkqxf").unwrap(),
            Pubkey::from_str("KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD").unwrap(),
            Pubkey::from_str("KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD").unwrap(),
            Pubkey::from_str("Sysvar1nstructions1111111111111111111111111").unwrap(),
            Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(),
        ]
    }

    pub fn get_meteora_addresses(&self) -> Vec<Pubkey> {
        vec![
            Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo").unwrap(), // lb_pair
            Pubkey::from_str("2Aw1uS9o3CV2qurdysR9vLR9qtoWzL5xwX7oJcnrY7TA").unwrap(),
            Pubkey::from_str("5GFFRSdJXK5vnsNLjfqYJKgvNcTrVdcXMCFek6Cs4Jbb").unwrap(),
            Pubkey::from_str("8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG").unwrap(),
            Pubkey::from_str("HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR").unwrap(), // lb_pair
            Pubkey::from_str("9HcJeBEsq5px2bYZbdo7vzQWVsPK3SHTkchy42hBn7HC").unwrap(), // bin_array_bitmap_extension
            Pubkey::from_str("H7j5NPopj3tQvDg4N8CxwtYciTn3e8AEV6wSVrxpyDUc").unwrap(), // reserve_x
            Pubkey::from_str("HbYjRzx7teCxqW3unpXBEcNHhfVZvW2vW9MQ99TkizWt").unwrap(), // reserve_y
            Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(), // reserve_y
            Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(), // reserve_y
            Pubkey::from_str("EgEYXef2FCoEYLHJJW74dMbom1atLXo6KwPuA6mSATYA").unwrap(), // oracle
            Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo").unwrap(), // oracle
            Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(), // oracle
            Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(), // oracle
            Pubkey::from_str("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr").unwrap(), // oracle
            Pubkey::from_str("D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6").unwrap(), // event_authority
            Pubkey::from_str("5Lw6FTwQAbfUns5iNfCpJFqBZjp8iQAMyRDwTqJQadSj").unwrap(), // bin_array0
            Pubkey::from_str("4tGcjYn8sf4RZ32uMPk2wiC9PzfrpubZZNephtnt9n5A").unwrap(), // bin_array1
            Pubkey::from_str("E3PAv7PgjMpX2TwAPHKuLFvMHi9t18dQ95Sva1XcUhp3").unwrap(), // bin_array2
        ]
    }

    pub fn get_pump_addresses(&self) -> Vec<Pubkey> {
        vec![
            Pubkey::from_str("Gf7sXMoP8iRw4iiXmJ1nq4vxcRycbGXy5RL8a8LnTd3v").unwrap(),
            Pubkey::from_str("2Aw1uS9o3CV2qurdysR9vLR9qtoWzL5xwX7oJcnrY7TA").unwrap(),
            Pubkey::from_str("8KSrnCnhZxDwvUuS56d6Fy19f8yeJ6BdmdydW3sWPRqG").unwrap(),
            Pubkey::from_str("5GFFRSdJXK5vnsNLjfqYJKgvNcTrVdcXMCFek6Cs4Jbb").unwrap(),
            Pubkey::from_str("Gf7sXMoP8iRw4iiXmJ1nq4vxcRycbGXy5RL8a8LnTd3v").unwrap(),
            Pubkey::from_str("ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw").unwrap(),
            Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(),
            Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),
            Pubkey::from_str("nML7msD1MiJHxFvhv4po1u6C4KpWr64ugKqc75DMuD2").unwrap(),
            Pubkey::from_str("EjHirXt2bQd2DDNveagHHCWYzUwtY1iwNbBrV5j84e6j").unwrap(),
            Pubkey::from_str("AVmoTthdrX6tKt4nDjco2D775W2YK3sDhxPcMmzUAmTY").unwrap(),
            Pubkey::from_str("FGptqdxjahafaCzpZ1T6EDtCzYMv7Dyn5MgBLyB3VUFW").unwrap(),
            Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(),
            Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(),
            Pubkey::from_str("11111111111111111111111111111111").unwrap(),
            Pubkey::from_str("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL").unwrap(),
            Pubkey::from_str("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR").unwrap(),
            Pubkey::from_str("Ei6iux5MMYG8JxCTr58goADqFTtMroL9TXJityF3fAQc").unwrap(),
            Pubkey::from_str("8N3GDaZ2iwN65oxVatKTLPNooAVUJTbfiVJ1ahyqwjSk").unwrap(),
            Pubkey::from_str("C2aFPdENg4A2HQsmrd5rTw5TaYBX5Ku887cWjbFKtZpw").unwrap(),
        ]
    }

    pub fn get_orca_addresses(&self) -> Vec<Pubkey> {
        vec![
            Pubkey::from_str("Czfq3xZZDmsdGdUyrNLtRhGc47cXcZtLG4crryfu44zE").unwrap(), // whirlpool
            Pubkey::from_str("EUuUbDcafPrmVTD5M6qoJAoyyNbihBhugADAxRMn5he9").unwrap(), // token_vault_a
            Pubkey::from_str("2WLWEuKDgkDUccTpbwYp1GToYktiSB1cXvreHUwiSUVP").unwrap(), // token_vault_b
            Pubkey::from_str("EpmYr9EDCdiZgPmdfTeEupXMyPoBKbwkPrutSPUJLua").unwrap(),  // tick_array0
            Pubkey::from_str("38d2DowiQEn1BUxqHWt38yp4pZHjDzU87hynZ7dLnmYJ").unwrap(), // tick_array1
            Pubkey::from_str("FoKYKtRpD25TKzBMndysKpgPqbj8AdLXjfpYHXn9PGTX").unwrap(), // oracle
            Pubkey::from_str("HiDHyvKAa33shG6jrdNEYmi8WCKmwhW47TzSK4jL7Ugj").unwrap(), // token_authority
        ]
    }

    // 从链上同步 ALT 数据到本地副本
    pub async fn sync_from_chain(&mut self) -> Result<(), AltError> {
        let alt_address = self.alt_account.ok_or(AltError::AltNotInitialized)?;

        // 获取链上 ALT 账户数据
        let alt_account_data = self.rpc_client.get_account(&alt_address)
            .map_err(|e| AltError::SyncFailed(format!("获取 ALT 账户失败: {}", e)))?;

        let alt_state = AddressLookupTable::deserialize(&alt_account_data.data)
            .map_err(|e| AltError::SyncFailed(format!("反序列化 ALT 数据失败: {}", e)))?;

        // 清空本地副本
        self.local_registry.clear();
        self.local_registry.alt_account = Some(alt_address.to_string());

        // 重新构建本地映射
        for (index, address) in alt_state.addresses.iter().enumerate() {
            if index >= 256 {
                break; // ALT 最大支持 256 个地址
            }

            let addr_str = address.to_string();
            self.local_registry.address_to_index.insert(addr_str.clone(), index as u8);
            self.local_registry.index_to_address.insert(index as u8, addr_str);
        }

        self.local_registry.next_index = alt_state.addresses.len() as u8;

        println!("同步完成，本地副本包含 {} 个地址", alt_state.addresses.len());
        Ok(())
    }

    // 保存本地副本到文件
    pub fn save_to_file(&self, file_path: &str) -> Result<(), AltError> {
        let serialized = serde_json::to_string_pretty(&self.local_registry)
            .map_err(|e| AltError::SerializationError(format!("序列化失败: {}", e)))?;

        std::fs::write(file_path, serialized)
            .map_err(|e| AltError::SerializationError(format!("写入文件失败: {}", e)))?;

        println!("本地副本已保存到: {}", file_path);
        Ok(())
    }

    // 从文件加载本地副本
    pub fn load_from_file(&mut self, file_path: &str) -> Result<(), AltError> {
        let content = std::fs::read_to_string(file_path)
            .map_err(|e| AltError::SerializationError(format!("读取文件失败: {}", e)))?;

        self.local_registry = serde_json::from_str(&content)
            .map_err(|e| AltError::SerializationError(format!("反序列化失败: {}", e)))?;

        self.alt_account = self.local_registry.alt_account
            .as_ref()
            .and_then(|s| Pubkey::from_str(s).ok());

        println!("本地副本已从文件加载: {}", file_path);
        Ok(())
    }

    // 检查本地副本和链上数据是否同步
    pub async fn check_sync_status(&self) -> Result<bool, AltError> {
        let alt_address = self.alt_account.ok_or(AltError::AltNotInitialized)?;

        let alt_account_data = self.rpc_client.get_account(&alt_address)
            .map_err(|e| AltError::SyncFailed(format!("获取 ALT 账户失败: {}", e)))?;

        let alt_state = AddressLookupTable::deserialize(&alt_account_data.data)
            .map_err(|e| AltError::SyncFailed(format!("反序列化 ALT 数据失败: {}", e)))?;

        // 比较地址数量
        if alt_state.addresses.len() != self.local_registry.len() {
            return Ok(false);
        }

        // 比较每个地址
        for (index, chain_address) in alt_state.addresses.iter().enumerate() {
            if let Some(local_address) = self.local_registry.get_address(index as u8) {
                if local_address != *chain_address {
                    return Ok(false);
                }
            } else {
                return Ok(false);
            }
        }

        Ok(true)
    }

    // 获取 ALT 状态信息
    pub async fn get_alt_info(&self) -> Result<(usize, bool), AltError> {
        let is_synced = self.check_sync_status().await.unwrap_or(false);
        let local_count = self.local_registry.len();
        Ok((local_count, is_synced))
    }

    // 自动重试的 ALT 扩展
    pub async fn extend_alt_with_retry(
        &mut self,
        new_addresses: Vec<Pubkey>,
        max_retries: u32
    ) -> Result<(), AltError> {
        let mut retry_count = 0;

        while retry_count < max_retries {
            match self.extend_alt(new_addresses.clone()).await {
                Ok(_) => return Ok(()),
                Err(e) => {
                    retry_count += 1;
                    eprintln!("ALT 扩展失败 (第{}次重试): {:?}", retry_count, e);

                    if retry_count < max_retries {
                        // 指数退避
                        let delay = std::time::Duration::from_millis(1000 * (2_u64.pow(retry_count)));
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }

        Err(AltError::ChainOperationFailed("扩展 ALT 重试次数超限".to_string()))
    }

    // 自动修复同步问题
    pub async fn auto_repair(&mut self) -> Result<(), AltError> {
        println!("检测到同步问题，开始自动修复...");

        // 1. 尝试从链上重新同步
        if let Err(e) = self.sync_from_chain().await {
            eprintln!("从链上同步失败: {:?}", e);

            // 2. 如果同步失败，尝试从备份文件恢复
            if let Err(e2) = self.load_from_file("alt_backup.json") {
                eprintln!("从备份恢复失败: {:?}", e2);
                return Err(AltError::SyncFailed("无法修复同步问题".to_string()));
            }

            println!("已从备份文件恢复本地副本");
        } else {
            println!("已从链上重新同步本地副本");
        }

        // 3. 保存修复后的状态
        let _ = self.save_to_file("alt_backup.json");

        Ok(())
    }

    // 健康检查
    pub async fn health_check(&mut self) -> Result<bool, AltError> {
        // 检查 ALT 账户是否存在
        if let Some(alt_address) = self.alt_account {
            match self.rpc_client.get_account(&alt_address) {
                Ok(_) => {
                    // 检查同步状态
                    match self.check_sync_status().await {
                        Ok(is_synced) => {
                            if !is_synced {
                                println!("检测到同步问题，开始自动修复");
                                self.auto_repair().await?;
                            }
                            Ok(true)
                        }
                        Err(e) => {
                            eprintln!("检查同步状态失败: {:?}", e);
                            Ok(false)
                        }
                    }
                }
                Err(e) => {
                    eprintln!("ALT 账户不存在或无法访问: {:?}", e);
                    Ok(false)
                }
            }
        } else {
            eprintln!("ALT 账户未初始化");
            Ok(false)
        }
    }

    // 初始化或恢复 ALT
    pub async fn initialize_or_recover(&mut self) -> Result<(), AltError> {
        // 尝试从文件加载
        if self.load_from_file("alt_backup.json").is_ok() {
            println!("从备份文件加载 ALT 配置");

            // 验证健康状态
            if self.health_check().await.unwrap_or(false) {
                return Ok(());
            }
        }

        // 如果加载失败或不健康，重新初始化
        println!("重新初始化 ALT");
        self.initialize_with_common_addresses().await?;

        // 保存新的配置
        let _ = self.save_to_file("alt_backup.json");

        Ok(())
    }

    // 推荐的初始化流程
    pub async fn recommended_setup(rpc_url: &str, payer: Keypair) -> Result<Self, AltError> {
        let mut manager = Self::new(rpc_url, payer);

        // 尝试初始化或恢复
        manager.initialize_or_recover().await?;

        // 执行健康检查
        manager.health_check().await?;

        println!("ALT 管理器初始化完成");
        Ok(manager)
    }

    // 在交易前的准备工作
    pub async fn prepare_for_transaction(&mut self, required_addresses: Vec<Pubkey>) -> Result<Vec<u8>, AltError> {
        let mut indices = Vec::new();
        let mut missing_addresses = Vec::new();

        // 检查哪些地址需要添加
        for address in &required_addresses {
            match self.get_address_index_safe(address) {
                Ok(index) => indices.push(index),
                Err(AltError::AddressNotFound(_)) => {
                    missing_addresses.push(*address);
                }
                Err(e) => return Err(e),
            }
        }

        // 添加缺失的地址
        if !missing_addresses.is_empty() {
            println!("添加 {} 个新地址到 ALT", missing_addresses.len());
            self.extend_alt_with_retry(missing_addresses.clone(), 3).await?;

            // 获取新添加地址的索引
            for address in missing_addresses {
                let index = self.get_address_index_safe(&address)?;
                indices.push(index);
            }
        }

        Ok(indices)
    }

    // 显示 ALT 状态
    pub async fn display_status(&self) {
        println!("=== ALT 管理器状态 ===");

        if let Some(alt_address) = self.alt_account {
            println!("ALT 地址: {}", alt_address);
            println!("本地副本地址数量: {}", self.local_registry.len());

            match self.get_alt_info().await {
                Ok((count, is_synced)) => {
                    println!("同步状态: {}", if is_synced { "已同步" } else { "需要同步" });
                }
                Err(e) => {
                    println!("获取状态失败: {:?}", e);
                }
            }
        } else {
            println!("ALT 未初始化");
        }

        println!("==================");
    }
}

#[derive(BorshSerialize, BorshDeserialize, Clone, Debug)]
pub struct IndexedAccountMeta {
    pub index: u8,
    pub is_signer: bool,
    pub is_writable: bool,
}

impl IndexedAccountMeta {
    pub fn new(index: u8, is_signer: bool, is_writable: bool) -> Self {
        Self {
            index,
            is_signer,
            is_writable,
        }
    }
}
